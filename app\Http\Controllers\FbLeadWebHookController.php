<?php

namespace App\Http\Controllers;

use App\Jobs\SendTaskJob;
use App\Models\Blacklist;
use App\Models\FacebookLeads;
use Illuminate\Http\Request;
use App\Traits\Whatsapp;
use App\Traits\Telegram;
use App\Traits\Bothandler;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use App\Models\Reply;
use App\Models\Device;
use App\Models\Contact;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Support\Facades\Log;
//composer require phpseclib/phpseclib:~3.0
use phpseclib3\Crypt\PublicKeyLoader;
use phpseclib3\Crypt\RSA;
use phpseclib3\Crypt\AES;
use Illuminate\Support\Str;
use GGInnovative\Larafirebase\Facades\Larafirebase;
use Illuminate\Support\Facades\Auth;

class FbLeadWebHookController extends Controller
{
    use Whatsapp;
    use Telegram;
    use Bothand<PERSON>;

    public function webhookHandler(Request $request)
    {
        // $authId = 3;
        // $authId = Auth::id();
        $input = json_decode(file_get_contents('php://input'), true);
        // Log::debug($input);

        //check if ''encrypted_flow_data' is present in the request

        if (isset($input['entry'][0]['changes'][0]['value']['leadgen_id']) && isset($input['entry'][0]['changes'][0]['value']['form_id'])) {
            // Access the 'leadgen_id' and 'form_id'
            $leadgen_id = $input['entry'][0]['changes'][0]['value']['leadgen_id'];
            $page_id = $input['entry'][0]['changes'][0]['value']['page_id'];
            $form_id = $input['entry'][0]['changes'][0]['value']['form_id'];

            // Log::debug('lead id:' . $leadgen_id);
            // Log::debug('form id:' . $form_id);
            // Log::debug('page id:' . $page_id);

            // Log::info('auth id is:' . $authId);

            $fbLead = FacebookLeads::where('page_id', $page_id)
                ->where('form_id', $form_id)->first();
            // Log::info('fbLead' . $fbLead);
            if ($fbLead) {
                $deviceId = $fbLead->device_id;
                // dd('device is ' . $deviceId);
                $userData = Device::where('id', $deviceId)->first();
                $userId = $userData->user_id;
                // dd('auth id is', $userId);

                $token = User::where('id', $userId)->first()->fb_access_token;
                // Log::info('tokenis:' . $token);
                // EAATAIZAChslIBO5VCOdtYRTzsFfyi8XgtryOSQYfZBD4lVzxzkoK0GSnXCNqZCFwoRU8zzBZB5u8IYrUoJRPvJWRRZAUdmt34IlbuYNzezlM5ZAXJFLgnsBIyhKisu5mrQeuLZCWoKcPTgIeqP2Mbgg7Jh9oMpNnwjGzv5gXmHD6HnvrSzYnoQj1D8uJxU7PCmnTRFmeS055pZAFtZCZCpLuVEOq89o09rKT40DxykjZBzMcCCJZCYsdk4PA0PEMRkaz
                $url = 'https://graph.facebook.com/v21.0/' . $leadgen_id . '?access_token=' . $token;
                // Log::info('url is ' . $url);

                $response = file_get_contents($url);

                $data = json_decode($response, true);

                // Log::info('data with lead gen id:' . $data);
                // Log::info('Data with leadgen id: ' . print_r($data, true));

                if (isset($data['field_data'])) {
                    $template = $fbLead->template;
                    $mediaType = $fbLead->tmp_type;
                    $mediaUrl = $fbLead->tmp_media;
                    $deviceId = $fbLead->device_id;
                    $tagId = $fbLead->tag_id;
                    $variable = $fbLead->variable;

                    $mobileNumber = null;
                    $fullName = null;
                    // Loop through the field_data to extract values
                    foreach ($data['field_data'] as $field) {
                        // Access the field name and values
                        $field_name = strtoupper($field['name']);
                        $field_values = $field['values'];

                        if (($field_name == 'PHONE_NUMBER' || $field_name == 'PHONE') && isset($field_values[0])) {
                            $mobileNumber = $field_values[0];  // Assuming it's the first value in the array
                            $mobileNumber = preg_replace('/[^0-9]/', '', $mobileNumber);
                            // Log::info('Mobile number: ' . $mobileNumber);
                        }

                        if ($field_name == 'FULL_NAME' && isset($field_values[0])) {
                            $fullName = $field_values[0];  // Assuming it's the first value in the array
                            // Log::info('full name is: ' . $fullName);
                        }

                        // You can now process each field as needed
                        // Log::info("Field Name: $field_name");
                        // Log::info("Field Values: " . implode(", ", $field_values));
                    }

                    if ($mobileNumber) {
                        $uuid = (string) Str::uuid();
                        $paraVar = ($variable == 'true') ? $fullName : null;
                        $taskData = [
                            'device_id' => $deviceId,
                            'created_by' => $userId,
                            'scheduled_on' => now(),
                            'send_to_number' => $mobileNumber,
                            'templateId' => $template,
                            'text' => null,
                            'task_type' => $mediaType ?? 1,
                            'parameters' => $paraVar,
                            'task_url' => $mediaUrl ?? null,
                            'ip' => $request->getClientIp(),
                            'whatsapp_id' => $uuid,
                            'campaign_name' => null,
                            'created_at' => now(),
                            'updated_at' => now()
                        ];

                        try {
                            // Log::info('store into task table');
                            DB::table('task')->insert($taskData);
                        } catch (\Exception $e) {
                            Log::info('faild to store task table' . $e);
                            return response()->json(['error' => 'Request Failed ' . $e], 401);
                        }

                        try {
                            // Log::info('sendtaskjob dispatch done');
                            dispatch(new SendTaskJob($uuid))->delay(now()->setTimeFromTimeString(now()))->onQueue('high');

                            $this->store_contact_tag($tagId, $mobileNumber, $userId, $fullName);
                        } catch (\Exception $e) {
                            Log::info('sendtaskjob dispatch failed');
                            return response()->json(['error' => 'Request Failed ' . $e], 401);
                        }
                    }
                } else {
                    Log::info("No field data found.");
                }
            } else {
                Log::info('no data found in database for given form and page id');
            }
        } else {
            Log::info("Required data not found!");
        }
    }

    public function subscribe(Request $request)
    {
        return response($request->get('hub_challenge'), 200);
    }

    public function store_contact_tag($tagid, $phone, $user_id, $name)
    {
        // Log::info('tagid: ' . $tagid);
        // Log::info('phone: ' . $phone);
        // Log::info('user: ' . $user_id);
        // Log::info('name: ' . $name);
        try {
            $contact = Contact::where('phone', $phone)->where('user_id', $user_id)->first();
            if ($contact) {
                //update
                $contact->tag_id = $tagid;
                $contact->save();
            } else {
                // create new
                $newContact = new Contact;
                $newContact->user_id = $user_id;
                $newContact->tag_id = $tagid;
                $newContact->phone = $phone;
                $newContact->name = $name;
                $newContact->save();
            }
            return response()->json(['message' => 'Tag added in contact'], 200);
        } catch (\Exception $e) {
            Log::info('faild to add contact in tag' . $e);
            return response()->json(['error' => 'Tag could not be added'], 500);
        }
    }
}
