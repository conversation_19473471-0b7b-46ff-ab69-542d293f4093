<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Config;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Models\Smstransaction;
use App\Models\Smstesttransactions;
use App\Http\Requests\Bulkrequest;
use App\Jobs\BulkInsertTaskJob;
use App\Models\User;
use App\Models\App;
use App\Models\Device;
use App\Models\Contact;
use App\Models\Template;
use App\Models\Reply;
use Carbon\Carbon;
use App\Traits\Whatsapp;
use Http;
use Auth;
use Illuminate\Support\Str;
use Session;
use Illuminate\Support\Facades\Validator;
use App\Jobs\SendTaskJob;
use App\Models\Group;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BulkController extends Controller
{
    use Whatsapp;


    /**
     * sent message
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function submitRequest(Bulkrequest $request)
    {
        $validator = Validator::make($request->all(), [
            'appkey' => 'required',
            'authkey' => 'required',
            'to' => 'required|regex:/^[0-9]+$/',
            'template_id' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors(),
            ], 400);
        }

        // dd($request->all());
        $user = User::where('status', 1)->where('will_expire', '>', now())->where('authkey', $request->authkey)->first();
        $app = App::where('key', $request->appkey)->whereHas('device')->with('device')->where('status', 1)->first();

        if (!empty($request->file)) {

            if (Str::length($request->file) > 100) {

                $file_url = $this->saveFileB64($request->file);
                $task_type = $this->identifyFileType($file_url);
            } else {
                $file_url = $request->file;
                $task_type = $this->identifyFileType($file_url);
            }

            $explode = explode('.', $file_url);
            $file_type = strtolower(end($explode));
            $extentions = [
                'jpg' => 'image',
                'jpeg' => 'image',
                'png' => 'image',
                'webp' => 'image',
                'mp4' => 'video',
                'pdf' => 'document',
                'docx' => 'document',
                'xlsx' => 'document',
                'csv' => 'document',
                'txt' => 'document'
            ];

            if (!isset($extentions[$file_type])) {
                $validators['error'] = 'file type should be jpg,jpeg,png,mp4,webp,pdf,docx,xlsx,csv,txt';
                return response()->json($validators, 403);
            }
            $media = $file_url;;
        }
        if ($user == null || $app == null) {
            return response()->json(['error' => 'Invalid Auth and AppKey'], 401);
        }

        if (getUserPlanData('messages_limit', $user->id) == false) {
            return response()->json([
                'message' => __('Maximum Monthly Messages Limit Exceeded')
            ], 401);
        }
        if (!empty($request->buttons)) {
            $buttons_array = $request->buttons;
            $buttons = null;

            foreach ($buttons_array as $key => $value) {

                if (strpos($key, '_type')) {
                    $index = intval(substr($key, 2, 1)) - 1; // get the button index

                    $buttons[$index] = [
                        'type' => 'button',
                        'sub_type' => $value,
                        'index' => $index,
                        'parameters' => [
                            [
                                'type' => 'payload',
                                'payload' => $buttons_array['{b' . ($index + 1) . '_value}'],
                            ],
                        ],
                    ];
                }
            }
            $buttons = json_encode($buttons);
        }
        if (!empty($request->variables)) {
            //array: [   "{variableKey1}" => "jhone"  "{variableKey2}" => "replaceable value"] to value string || seperated
            $parameters = implode(
                '||',
                array_map(
                    function ($v, $k) {
                        return sprintf("%s", $v);
                    },
                    $request->variables,
                    array_keys($request->variables)
                )
            );
        }
        $uuid = (string) Str::uuid();
        $inputSchedule = $request->schedule;
        $scheduleOn = $inputSchedule ? Carbon::createFromFormat('d-m-Y H:i', $inputSchedule)->format('Y-m-d\TH:i') : now();

        $data[] = [
            'device_id' => $app->device_id,
            'created_by' => $app->user_id,
            'scheduled_on' => $scheduleOn,
            'send_to_number' => $request->to,
            'templateId' => $request->template_id,
            'text' => null,
            'task_type' => $task_type ?? 1,
            'parameters' => $parameters ?? null,
            'task_url' => $media ?? null,
            'ip' => $request->getClientIp(),
            'whatsapp_id' => $uuid,
            'campaign_name' => $request->file_name ?? null,
            'created_at' => now(),
            'updated_at' => now()
        ];

        try {
            DB::table('task')->insert($data);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Request Failed ' . $e], 401);
        }


        try {

            // dispatch(new SendTaskJob($uuid))->delay(now()->setTimeFromTimeString(now()));
            // use high priority queue
            SendTaskJob::dispatch($uuid)->onQueue('high')->delay(now()->setTimeFromTimeString($scheduleOn));
            return response()->json([
                'message_status' => 'Success',
                'data' => [
                    'from' => $app->device->phone ?? null,
                    'to' => $request->to,
                    'id' => $uuid,
                    'status_code' => 200,
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Request Failed ' . $e], 401);
        }
    }

    public function submitRequestJson(Bulkrequest $request)
    {
        $validator = Validator::make($request->all(), [
            'appkey' => 'required',
            'authkey' => 'required',
            // 'to' => 'required|regex:/^[0-9]+$/',
            'template_id' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors(),
            ], 400);
        }

        // dd($request->all());
        // $accessToken = $response->json('access_token');
        $user = User::where('status', 1)->where('will_expire', '>', now())->where('authkey', $request->json('authkey'))->first();
        $app = App::where('key', $request->json('appkey'))->whereHas('device')->with('device')->where('status', 1)->first();

        if (!empty($request->json('file'))) {

            if (Str::length($request->json('file')) > 100) {

                $file_url = $this->saveFileB64($request->json('file'));
                $task_type = $this->identifyFileType($file_url);
            } else {
                $file_url = $request->json('file');
                $task_type = $this->identifyFileType($file_url);
            }

            $explode = explode('.', $file_url);
            $file_type = strtolower(end($explode));
            $extentions = [
                'jpg' => 'image',
                'jpeg' => 'image',
                'png' => 'image',
                'webp' => 'image',
                'mp4' => 'video',
                'pdf' => 'document',
                'docx' => 'document',
                'xlsx' => 'document',
                'csv' => 'document',
                'txt' => 'document'
            ];

            if (!isset($extentions[$file_type])) {
                $validators['error'] = 'file type should be jpg,jpeg,png,mp4,webp,pdf,docx,xlsx,csv,txt';
                return response()->json($validators, 403);
            }
            $media = $file_url;;
        }
        if ($user == null || $app == null) {
            return response()->json(['error' => 'Invalid Auth and AppKey'], 401);
        }

        if (getUserPlanData('messages_limit', $user->id) == false) {
            return response()->json([
                'message' => __('Maximum Monthly Messages Limit Exceeded')
            ], 401);
        }
        if (!empty($request->json('buttons'))) {
            $buttons_array = $request->json('buttons');
            $buttons = null;

            foreach ($buttons_array as $key => $value) {
                if (preg_match('/^b\d+_type$/', $key)) {
                    // dd('hellosssdf');
                    $index = intval(preg_replace('/\D/', '', $key)) - 1; // get the button index

                    $buttons[$index] = [
                        'type' => 'button',
                        'sub_type' => $value,
                        'index' => $index,
                        'parameters' => [
                            [
                                'type' => 'payload',
                                'payload' => $buttons_array["b" . ($index + 1) . "_value"] ?? '',
                            ],
                        ],
                    ];
                }
            }
            $buttons = json_encode($buttons);
        }
        if (!empty($request->json('variables'))) {
            //array: [   "{variableKey1}" => "jhone"  "{variableKey2}" => "replaceable value"] to value string || seperated
            $parameters = implode(
                '||',
                array_map(
                    function ($v, $k) {
                        return sprintf("%s", $v);
                    },
                    $request->json('variables'),
                    array_keys($request->json('variables'))
                )
            );
        }
        $uuid = (string) Str::uuid();

        //$inputSchedule = $request->schedule;
        //$scheduleOn = $inputSchedule ? Carbon::createFromFormat('d-m-Y H:i', $inputSchedule)->format('Y-m-d\TH:i') : now();
        // 
        $campaign_numbers = $request->to;

        // Log::info('camo', $campaign_numbers);

        // Log::info('campaign_numbers_array', $campaign_numbers);

        if (count($campaign_numbers) > 500) {
            $scheduled_on = $request->json('schedule') ?? now()->addMinutes(5);
        } else {
            $scheduled_on = $request->json('schedule') ?? now();
        }

        $taskdata = [
            'device_id' => $app->device_id,
            'created_by' => $app->user_id,
            'scheduled_on' => $scheduled_on,
            'task_url' => $media ?? null,
            'campaign_name' => $request->json('file_name') ?? null,
            'templateId' => $request->json('template_id'),
            'task_type' => $task_type ?? 1,
            'parameters' => $parameters ?? null,
            'text' => null,
            'ip' => $request->getClientIp(),
            'created_at' => now(),
            'updated_at' => now()
        ];

        try {
            BulkInsertTaskJob::dispatch($taskdata, $campaign_numbers)->onQueue('high');

            return response()->json([
                'message_status' => 'Success',
                'data' => [
                    'status_code' => 200,
                ]
            ], 200);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error dispatching the job: ' . $e->getMessage());
        }
    }

    public function generateButtons($request)
    {
        $buttons = [];

        foreach ($request->all() as $key => $value) {
            if ($key === 'b1_type' && $value !== null && $value !== '') {
                if ($value !== 'quick_reply' && $value !== 'url') {
                    echo 'Invalid Button 1 Type' . PHP_EOL;
                    return null;
                }

                $buttons[] = [
                    'type' => 'button',
                    'sub_type' => $value,
                    'index' => 0,
                    'parameters' => [
                        [
                            'type' => 'payload',
                            'payload' => $request->input('b1_param'),
                        ],
                    ],
                ];
            }

            if ($key === 'b2_type' && $value !== null && $value !== '') {
                if ($value !== 'quick_reply' && $value !== 'url') {
                    echo 'Invalid Button 2 Type' . PHP_EOL;
                    return null;
                }

                $buttons[] = [
                    'type' => 'button',
                    'sub_type' => $value,
                    'index' => 1,
                    'parameters' => [
                        [
                            'type' => 'payload',
                            'payload' => $request->input('b2_param'),
                        ],
                    ],
                ];
            }

            if ($key === 'b3_type' && $value !== null && $value !== '') {
                if ($value !== 'quick_reply' && $value !== 'url') {
                    echo 'Invalid Button 3 Type' . PHP_EOL;
                    return null;
                }

                $buttons[] = [
                    'type' => 'button',
                    'sub_type' => $value,
                    'index' => 2,
                    'parameters' => [
                        [
                            'type' => 'payload',
                            'payload' => $request->input('b3_param'),
                        ],
                    ],
                ];
            }
        }

        if (empty($buttons)) {
            return null;
        }

        return json_encode($buttons);
    }

    /**
     * set status device
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function setStatus($device_id, $status)
    {

        $device_id = str_replace('device_', '', $device_id);

        $device = Device::where('id', $device_id)->first();
        if (!empty($device)) {
            $device->status = $status;
            $device->save();
        }
    }


    /**
     * receive webhook response
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function webHook(Request $request, $device_id)
    {

        $session = $device_id;
        $device_id = str_replace('device_', '', $device_id);

        $device = Device::with('user')->whereHas('user', function ($query) {
            return $query->where('will_expire', '>', now());
        })->where('id', $device_id)->first();

        $request_from = explode('@', $request->from);
        $request_from = $request_from[0];

        $message_id = $request->message_id ?? null;
        $message = json_encode($request->message ?? '');
        $message = json_decode($message);

        $device_id = $device_id;

        if (isset($message->conversation)) {
            $message = $message->conversation ?? null;
        } elseif (isset($message->extendedTextMessage)) {
            $message = $message->extendedTextMessage->text ?? null;
        } elseif (isset($message->buttonsResponseMessage)) {
            $message = $message->buttonsResponseMessage->selectedDisplayText ?? null;
        } elseif (isset($message->listResponseMessage)) {
            $message = $message->listResponseMessage->title ?? null;
        } else {
            $message = null;
        }

        info($message);


        if ($device != null && $message != null) {
            $replies = Reply::where('device_id', $device_id)->with('template')->where('keyword', 'LIKE', '%' . $message . '%')->latest()->get();

            foreach ($replies as $key => $reply) {
                if ($reply->match_type == 'equal') {

                    if ($reply->reply_type == 'text') {

                        $logs['user_id'] = $device->user_id;
                        $logs['device_id'] = $device->id;
                        $logs['from'] = $device->phone ?? null;
                        $logs['to'] = $request_from;
                        $logs['type'] = 'chatbot';
                        $this->saveLog($logs);

                        return response()->json([
                            'message' => array('text' => $reply->reply),
                            'receiver' => $request->from,
                            'session_id' => $session
                        ], 200);
                    } else {
                        if (!empty($reply->template)) {
                            $template = $reply->template;

                            if (isset($template->body['text'])) {
                                $body = $template->body;
                                $text = $this->formatText($template->body['text'], [], $device->user);
                                $body['text'] = $text;
                            } else {
                                $body = $template->body;
                            }

                            $logs['user_id'] = $device->user_id;
                            $logs['device_id'] = $device->id;
                            $logs['from'] = $device->phone ?? null;
                            $logs['to'] = $request_from;
                            $logs['type'] = 'chatbot';
                            $logs['template_id'] = $template->id ?? null;
                            $this->saveLog($logs);

                            return response()->json([
                                'message' => $body,
                                'receiver' => $request->from,
                                'session_id' => $session
                            ], 200);
                        }
                    }
                    break;
                }
            }
        }

        return response()->json([
            'message' => array('text' => null),
            'receiver' => $request->from,
            'session_id' => $session
        ], 403);
    }

    public function saveFileB64($base64)
    {
        $decodedData = base64_decode($base64);
        // $ext = mime_content_type($decodedData);
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime = finfo_buffer($finfo, $decodedData);
        finfo_close($finfo);
        $extensions = [
            'image/jpeg' => 'jpg',
            'image/png'  => 'png',
            'application/pdf' => 'pdf',
            'video/mp4' => 'mp4',
            'video/mpeg' => 'mpeg',
            'text/plain' => 'txt',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'xlsx',
            'text/csv' => 'csv',
        ];
        $fileExtension = $extensions[$mime] ?? null;
        $randomString = bin2hex(random_bytes(5));
        $filename = now()->timestamp . $randomString . '.' . $fileExtension;

        $path = 'whatsappmedia/';
        $filePath = $path . $filename;


        Storage::put($filePath, $decodedData);

        return asset($filePath);
    }

    public function identifyFileType($url)
    {
        $headers = get_headers($url, 1);

        if (isset($headers['Content-Type'])) {
            $contentType = $headers['Content-Type'];

            if (strpos($contentType, 'text') !== false) {
                return 1; // Text
            } elseif (strpos($contentType, 'image') !== false) {
                return 2; // Image
            } elseif (strpos($contentType, 'application/pdf') !== false || strpos($contentType, 'application/msword') !== false || strpos($contentType, 'application/vnd.ms-excel') !== false || strpos($contentType, 'application/vnd.ms-powerpoint') !== false || strpos($contentType, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') !== false || strpos($contentType, 'application/vnd.openxmlformats-officedocument.presentationml.presentation') !== false || strpos($contentType, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') !== false) {
                return 3; // Document
            } elseif (strpos($contentType, 'video/mp4') !== false) {
                return 4; // Video
            } else {
                return 1; // Other or unknown type
            }
        }

        return 1; // No Content-Type header found
    }

    public function getTemplates(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'appkey' => 'required',
            'authkey' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors(),
            ], 400);
        }

        $user = User::where('status', 1)->where('will_expire', '>', now())->where('authkey', $request->authkey)->first();
        $app = App::where('key', $request->appkey)->whereHas('device')->with('device')->where('status', 1)->first();

        //Log::info('user is ' . $user);
        if ($user == null || $app == null) {
            Log::info('Invalid Auth or AppKey');
            return response()->json(['error' => 'Invalid Auth and AppKey'], 401);
        }

        if (getUserPlanData('messages_limit', $user->id) == false) {
            Log::info('Maximum Monthly Messages Limit Exceeded');
            return response()->json([
                'message' => __('Maximum Monthly Messages Limit Exceeded')
            ], 401);
        }

        $deviceData = $app->device;

        $fbTemplates = getFacebookTemplates($deviceData['waid'], $deviceData['token']);

        return response()->json([
            'message_status' => 'Success',
            'data' => $fbTemplates
        ], 200);
    }

    public function typebotMessageLog(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'appkey' => 'required',
            'authkey' => 'required',
            'to' => 'required',
            'text' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors(),
            ], 400);
        }

        $user = User::where('status', 1)->where('will_expire', '>', now())->where('authkey', $request->authkey)->first();
        $app = App::where('key', $request->appkey)->whereHas('device')->with('device')->where('status', 1)->first();

        $uuid = (string) Str::uuid();
        $data = [
            'device_id' => $app->device_id,
            'created_by' => $app->user_id,
            'scheduled_on' => now(),
            'whatsapp_sent_time' => now(),
            'send_to_number' => $request->to,
            'templateId' => 'auto_reply',
            'text' => $request->text,
            'task_type' => 1,
            'task_status' => 1,
            'parameters' => null,
            'task_url' => null,
            'ip' => $request->getClientIp(),
            'whatsapp_id' => $uuid,
            'campaign_name' => null,
            'created_at' => now(),
            'updated_at' => now()
        ];

        try {
            DB::table('task')->insert($data);

            return response()->json([
                'message_status' => 'Success',
                'data' => [
                    'status_code' => 200,
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Insert Failed To Task Table ' . $e], 401);
        }
    }
}
