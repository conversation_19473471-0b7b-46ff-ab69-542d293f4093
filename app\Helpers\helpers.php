<?php

use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

if (!function_exists('getFacebookTemplates')) {
    function getFacebookTemplates($waid, $token)
    {
        $version = env('FACEBOOK_VERSION', 'v18.0');
        $url = "https://graph.facebook.com/{$version}/{$waid}/message_templates";
        $headers = [
            'Authorization' => "Bearer {$token}",
        ];

        $allData = [];
        $after = null;
        $maxRetries = 3;
        $retryDelay = 2000; // milliseconds

        do {
            $attempt = 0;
            $success = false;
            
            while (!$success && $attempt < $maxRetries) {
                try {
                    $pagedUrl = $after ? $url . '?after=' . $after : $url;
                    $response = Http::timeout(30)->withHeaders($headers)->get($pagedUrl);
                    $success = true;
                } catch (\Exception $e) {
                    $attempt++;
                    if ($attempt >= $maxRetries) {
                        Log::error("Facebook API connection failed after {$maxRetries} attempts: " . $e->getMessage());
                        throw $e;
                    }
                    usleep($retryDelay * 1000); // Convert to microseconds
                }
            }
            
            if ($response->successful()) {
                $data = $response->json('data');

                // If data is blank, exit the loop
                if (empty($data)) {
                    break;
                }

                // Filter out unwanted templates
                $ignoredTemplates = [
                    'sample_flight_confirmation',
                    'sample_happy_hour_announcement',
                    'sample_movie_ticket_confirmation',
                    'sample_issue_resolution',
                    'sample_purchase_feedback',
                    'sample_shipping_confirmation',
                ];

                $filteredData = array_filter($data, function ($item) use ($ignoredTemplates) {
                    return !in_array($item['name'], $ignoredTemplates);
                });

                $allData = array_merge($allData, $filteredData);

                $paging = $response->json('paging');
                $after = $paging['cursors']['after'] ?? null;
            } else {
                $after = null;
            }
        } while ($after);

        return $allData;
    }
}

if (!function_exists('postBlockUsers')) {
    function postBlockUsers($phoneId, $token, $number)
    {
        $version = env('FACEBOOK_VERSION', 'v18.0');
        $url = "https://graph.facebook.com/{$version}/{$phoneId}/block_users";

        $bodyData = [
            'messaging_product' => 'whatsapp',
            'block_users' => [
                [
                    'user' => $number
                ]
            ]
        ];
        try {
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$token}",
            ])->post($url, $bodyData);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'message' => "{$number} blocked successfully.",
                ];
            } else {
                Log::error("Failed to block {$number}: " . $response->body());
                return [
                    'success' => false,
                    'message' => "Failed to block {$number}.",
                ];
            }
        } catch (\Exception $e) {
            Log::error("Exception occurred while blocking {$number}: " . $e->getMessage());
            return [
                'success' => false,
                'message' => "An error occurred while blocking {$number}.",
            ];
        }
    }
}

if (!function_exists('deleteBlockUsers')) {
    function deleteBlockUsers($phoneId, $token, $number)
    {
        $version = env('FACEBOOK_VERSION', 'v18.0');
        $url = "https://graph.facebook.com/{$version}/{$phoneId}/block_users";

        $bodyData = [
            'messaging_product' => 'whatsapp',
            'block_users' => [
                [
                    'user' => $number
                ]
            ]
        ];
        try {
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$token}",
            ])->delete($url, $bodyData);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'message' => "{$number} unblocked successfully.",
                ];
            } else {
                Log::error("Failed to unblock {$number}: " . $response->body());
                return [
                    'success' => false,
                    'message' => "Failed to unblock {$number}.",
                ];
            }
        } catch (\Exception $e) {
            Log::error("Exception occurred while unblocking {$number}: " . $e->getMessage());
            return [
                'success' => false,
                'message' => "An error occurred while unblocking {$number}.",
            ];
        }
    }
}

