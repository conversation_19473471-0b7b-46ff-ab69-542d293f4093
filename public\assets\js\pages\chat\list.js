'use strict'


    const device_id = (() => {
        const $uuidElement = $('#uuid');
        if ($uuidElement.length > 0) {
            const val = $uuidElement.val();
            if (typeof val === 'string' && val.length > 0) {
                return val;
            }
            const dataUuid = $uuidElement.data('uuid');
            if (typeof dataUuid === 'string' && dataUuid.length > 0) {
                return dataUuid;
            }
        }

        // Fallback to URL parsing
        const pathParts = window.location.pathname.split('/');
        const potentialUuid = pathParts[pathParts.length - 1];
        if (potentialUuid && potentialUuid.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
            return potentialUuid;
        }

        return ''; // Final fallback
    })();

    const base_url = $('#base_url').val();
    const whatsappicon = base_url + '/assets/img/whatsapp.png';

    let page = 1;
    let lastsearch = '';
    let loading = false;
    let last_timestamp = getFormattedTimestamp();

    getChatList();

function getChatList() {
    if (!loading) { // Check if a request is already in progress
        loading = true; // Set loading flag

    var day = document.getElementById('chat_days').value
    var status = document.getElementById('chat_status').value
    var tag = document.getElementById('chat_tagi').value
    var consearch = document.getElementById('consearch').value

    // console.log('Day:', day);
    // console.log('Status:', status);
    // console.log('Tag:', tag);
    // console.log('Search:', consearch);
    // console.log('----------');

    if(lastsearch.length > 0 && consearch.length === 0){
        page = 1;
        lastsearch ='';
        $('.contact-list').empty();
    }
    if(consearch.length>3){
        page = 1;
        lastsearch = consearch;
    }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        },
    })
    $('.qr-area').show()
    $.ajax({
        type: 'POST',
        url: base_url + '/user/get-chats/' + device_id,
        dataType: 'json',
        data: { day: day, status: status, tag: tag ,page:page, consearch:consearch},
        success: function (response) {            
            $('.qr-area').hide()
            if (response.hasOwnProperty('chats')) {
                let transformedItems = response.chats.data.map(item => {
                    let unread_count = item.unreadcount !== null ? parseInt(item.unreadcount) : 0;
                    let tag_name = item.tag_name !== null ? item.tag_name : 'All';
                    let data = {
                        number: item.sender_no,
                        name: item.name !== null ? item.name : item.sender_no,
                        unread: unread_count,
                        timestamp: item.timestamp !== null ? parseInt(Date.parse(item.timestamp)) : 0,
                        tag_id: item.tag_id !== null ? parseInt(item.tag_id) : 0,
                        tag_name: tag_name,
                        tag_color: item.color_code !== null ? item.color_code : null,
                        blacklist_info: item.is_blacklisted ?? 0
                    };
                    return data;
                });
               // console.log (transformedItems);
          //  const chats = sortByKey(response.chats, 'timestamp')
                if(consearch.length>3){
            $('.contact-list').empty()
        }
            $.each(transformedItems, function (key, item) {
                if (item.timestamp > 0) {
                    var time = formatTimestamp(item.timestamp)
                    time = `<span class="text-success">${time}</span>`
                } else {
                    var time = ''
                }
                if (item.unread > 0) {
                    unread = `<span class="float-right unread_${item.number}" style="display: inline-block; background-color: #25D366;color: #ffff; border-radius: 50%; width: 18px; height: 18px; text-align: center; line-height: 20px; font-size: 12px; margin-right: 12px;">${item.unread}</span>`
                } else {
                    var unread = ''
                }
                var tag_icon = '';
                if (item.tag_name === 'All' || item.tag_name === null) {
                    tag_icon = '';
                } else {
                    tag_icon = `<a style="cursor:pointer;" data-bs-toggle="tooltip" title="${item.tag_name}">
                                    <svg width="30" height="30" viewBox="0 0 30 30" id="chat_tag">
                                    <path d="M17 3H7c-1.1 0-2 .9-2 2v18l7-7 7 7V5c0-1.1-.9-2-2-2" fill="${item.tag_color}" />
                                    </svg>
                                </a>`
                }
                var blacklist_icon ='';
                if (item.blacklist_info) {
                    blacklist_icon = `<svg width="30" height="30" viewBox="0 0 30 30">
                            <circle cx="12" cy="12" r="10" stroke="#FA0000" stroke-width="2" fill="none" />
                            <line x1="4" y1="4" x2="20" y2="20" stroke="#FA0000"
                                stroke-width="2" />
                        </svg>`
                } else {
                     blacklist_icon = ''
                }

                var html = `<li class="list-group-item px-0 contact contact${key}" >
				<div class="row align-items-center">
				<div class="col-auto">
				<a href="javascript:void(0)" data-active=".contact${key}" data-name="${item.name}" data-number="${
                    item.number
                }" class="avatar rounded-circle wa-link ml-2">
				<img alt="" src="${whatsappicon}">
				</a>
				</div>
				<div class="col ml--2">
				<h4 class="mb-0">
				<a href="javascript:void(0)" data-active=".contact${key}" class="wa-link" data-tagname="${
                    item.tag_name ?? 'All'
                }" data-unread="${item.unread ?? 0}" data-number="${item.number}" data-name="${item.name}">${
                    item.name
                }<br><small>(${item.number})</small> </a>
                 ${tag_icon}
                 ${blacklist_icon}
                 <li class="nav-item dropdown" id="deletechatids" style="display: flex; justify-content: end; align-items: center; height: 0vh; margin: 0;">
                <a data-toggle="dropdown" aria-expanded="false" data-animation="scale-up" role="button">
                    <svg viewBox="0 0 24 24" height="20" width="20" preserveAspectRatio="xMidYMid meet" class="" version="1.1" x="0px" y="0px" enable-background="new 0 0 24 24" style="border: 1px solid;border-radius: 50%;padding: 1px;margin-bottom: 50px;margin-right: 11px;">
                        <path d="M12,7c1.104,0,2-0.896,2-2c0-1.105-0.895-2-2-2c-1.104,0-2,0.894-2,2 C10,6.105,10.895,7,12,7z M12,9c-1.104,0-2,0.894-2,2c0,1.104,0.895,2,2,2c1.104,0,2-0.896,2-2C13.999,9.895,13.104,9,12,9z M12,15 c-1.104,0-2,0.894-2,2c0,1.104,0.895,2,2,2c1.104,0,2-0.896,2-2C13.999,15.894,13.104,15,12,15z">
                        </path>
                    </svg>
                </a>
                <div class="dropdown-menu dropdown-menu-right dropdown-menu-media" role="menu">
                    <div class="list-group">
                        <div data-role="container">
                            <div data-role="content">
                                <a class="list-group-item dropdown-item" role="chatdelete">
                                    <div class="chat_delete">
                                        <div class="form-group">
                                            <h5>Are You Sure To Delete Chat ?</h5>
                                        </div>
                                        <div class="form-group">
                                        <button class="btn btn-outline-danger submit-button btn-block" onclick="deleteChat('${device_id}', '${
                    item.number
                }')">Delete Chat</button>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </li>
				</h4>
				${time}
				${unread}
				</div>
				</div>
				</li>`
                //empty contact list

                $('.contact-list').append(html)
            })

            page++;

                loading = false; // Reset loading flag
        }
        },
        error: function (xhr, status, error) {
            if (xhr.status == 500) {
                loading = false;
            }
        },
    })
}}

function getnewChatList() {
    // console.log('getnewChatList called');
    
    if (!loading) { // Check if a request is already in progress
        loading = true; // Set loading flag

    var day = document.getElementById('chat_days').value
    var status = document.getElementById('chat_status').value
    var tag = document.getElementById('chat_tagi').value
    var consearch = document.getElementById('consearch').value
    if (device_id === null || device_id === undefined || device_id.trim() === '') {
        window.location.href = '/';
    }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        },
    })

    $.ajax({
        type: 'POST',
        url: base_url + '/user/get-chats/' + device_id,
        dataType: 'json',
        data: { day: null, status: null, tag: null ,page:1, consearch:null , lastTimesamp: last_timestamp},
        success: function (response) {

            if (response.hasOwnProperty('chats')) {
                last_timestamp = getFormattedTimestamp();
                let transformedItems = response.chats.data.map(item => {
                    let unread_count = item.unreadcount !== null ? parseInt(item.unreadcount) : 0;
                    let tag_name = item.tag_name !== null ? item.tag_name : 'All';
                    let data = {
                        number: item.sender_no,
                        name: item.name !== null ? item.name : item.sender_no,
                        unread: unread_count,
                        timestamp: item.timestamp !== null ? parseInt(Date.parse(item.timestamp)) : 0,
                        tag_id: item.tag_id !== null ? parseInt(item.tag_id) : 0,
                        tag_name: tag_name,
                        tag_color: item.color_code !== null ? item.color_code : null,
                        blacklist_info: item.is_blacklisted ?? 0
                    };
                    return data;
                });
               // console.log (transformedItems);
          //  const chats = sortByKey(response.chats, 'timestamp')

            $.each(transformedItems, function (key, item) {
                if (item.timestamp > 0) {
                    var time = formatTimestamp(item.timestamp)
                    time = `<span class="text-success">${time}</span>`
                } else {
                    var time = ''
                }
                if (item.unread > 0) {
                    unread = `<span class="float-right unread_${item.number}" style="display: inline-block; background-color: #25D366;color: #ffff; border-radius: 50%; width: 18px; height: 18px; text-align: center; line-height: 20px; font-size: 12px; margin-right: 12px;">${item.unread}</span>`
                } else {
                    var unread = ''
                }
                var tag_icon = '';
                if (item.tag_name === 'All' || item.tag_name === null) {
                    tag_icon = ''
                } else {
                    tag_icon = `<a style="cursor:pointer;" data-bs-toggle="tooltip" title="${item.tag_name}">
                                    <svg width="30" height="30" viewBox="0 0 30 30" id="chat_tag">
                                    <path d="M17 3H7c-1.1 0-2 .9-2 2v18l7-7 7 7V5c0-1.1-.9-2-2-2" fill="${item.tag_color}" />
                                    </svg>
                                </a>`
                }
                var blacklist_icon ='';
                if (item.blacklist_info) {
                    blacklist_icon = `<svg width="30" height="30" viewBox="0 0 30 30">
                            <circle cx="12" cy="12" r="10" stroke="#FA0000" stroke-width="2" fill="none" />
                            <line x1="4" y1="4" x2="20" y2="20" stroke="#FA0000"
                                stroke-width="2" />
                        </svg>`
                } else {
                     blacklist_icon = ''
                }

                var html = `<li class="list-group-item px-0 contact contact${key}" >
				<div class="row align-items-center">
				<div class="col-auto">
				<a href="javascript:void(0)" data-active=".contact${key}" data-name="${item.name}" data-number="${
                    item.number
                }" class="avatar rounded-circle wa-link ml-2">
				<img alt="" src="${whatsappicon}">
				</a>
				</div>
				<div class="col ml--2">
				<h4 class="mb-0">
				<a href="javascript:void(0)" data-active=".contact${key}" class="wa-link" data-tagname="${
                    item.tag_name ?? 'All'
                }" data-unread="${item.unread ?? 0}" data-number="${item.number}" data-name="${item.name}">${
                    item.name
                }<br><small>(${item.number})</small> </a>
                 ${tag_icon}
                 ${blacklist_icon}
                 <li class="nav-item dropdown" id="deletechatids" style="display: flex; justify-content: end; align-items: center; height: 0vh; margin: 0;">
                <a data-toggle="dropdown" aria-expanded="false" data-animation="scale-up" role="button">
                    <svg viewBox="0 0 24 24" height="20" width="20" preserveAspectRatio="xMidYMid meet" class="" version="1.1" x="0px" y="0px" enable-background="new 0 0 24 24" style="border: 1px solid;border-radius: 50%;padding: 1px;margin-bottom: 50px;margin-right: 11px;">
                        <path d="M12,7c1.104,0,2-0.896,2-2c0-1.105-0.895-2-2-2c-1.104,0-2,0.894-2,2 C10,6.105,10.895,7,12,7z M12,9c-1.104,0-2,0.894-2,2c0,1.104,0.895,2,2,2c1.104,0,2-0.896,2-2C13.999,9.895,13.104,9,12,9z M12,15 c-1.104,0-2,0.894-2,2c0,1.104,0.895,2,2,2c1.104,0,2-0.896,2-2C13.999,15.894,13.104,15,12,15z">
                        </path>
                    </svg>
                </a>
                <div class="dropdown-menu dropdown-menu-right dropdown-menu-media" role="menu">
                    <div class="list-group">
                        <div data-role="container">
                            <div data-role="content">
                                <a class="list-group-item dropdown-item" role="chatdelete">
                                    <div class="chat_delete">
                                        <div class="form-group">
                                            <h5>Are You Sure To Delete Chat ?</h5>
                                        </div>
                                        <div class="form-group">
                                        <button class="btn btn-outline-danger submit-button btn-block" onclick="deleteChat('${device_id}', '${
                    item.number
                }')">Delete Chat</button>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </li>
				</h4>
				${time}
				${unread}
				</div>
				</div>
				</li>`
                //empty contact list

                $(html).prependTo('.contact-list');
            })

            page++;

                loading = false; // Reset loading flag
        }
        },
        error: function (xhr, status, error) {
            if (xhr.status == 500) {
                loading = false;
            }
        },
    })
}}


$('.contact-list').scroll(function() {
    let element = $(this)[0];
    let scrollPosition = element.scrollTop;
    let totalHeight = element.scrollHeight;
  
    // Calculate the 50% scroll position
    let fiftyPercentScroll = totalHeight * 0.5;

    if (scrollPosition >= fiftyPercentScroll) {
        // If scrolled to 50% or more, fetch more data
        getChatList();
    }
});

function deleteChat(device_uuid, number) {
    var data = {
        device_uuid: device_uuid,
        number: number,
    }
    $.ajax({
        url: '/user/chat/delete/' + device_uuid + '/' + number,
        type: 'POST',
        data: data,
        dataType: 'JSON',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        },
        success: function (data) {
            if (data.message) {
                setTimeout(function () {
                    getchathistory(number)
                    $('#tagrecivername').val('')
                }, 2000)
                setTimeout(function () {
                    getChatList()
                }, 2500)
            } else {
                console.error('Error deleting device chat')
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            // Handle error
            console.error('Error deleting chat')
        },
    })
}

function successCallBack() {
    $('#plain-text').val('')
}

$(document).on('click', '.wa-link', function () {
    const phone = $(this).data('number')
    const name = $(this).data('name')
    const activeTarget = $(this).data('active')
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        },
    })
    $.ajax({
        type: 'GET',
        url: base_url + '/user/markread/' + device_id + '/' + phone,
    })

    $('.unread_' + phone).remove()
    $('.contact').removeClass('active')
    $(activeTarget).addClass('active')
    $('.chat-list').html(phone)
    $('.sendble-row').removeClass('none')
    $('.reciver-number').val(phone)
    $('.reciver-name').val(name)
    getchathistory(phone)
})

function getExtension(url) {
    const parts = url.split('/').pop().split('?')[0].split('.')
    return parts[1]
}

// this function use for show mobile number chat right side..
function getchathistory(phone) {

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        },
    })
    $.ajax({
        type: 'GET',
        url: base_url + '/user/chats-history/' + device_id + '/' + phone,
        dataType: 'json',
        success: function (response) {
            // console.log('chat response is ',response);
            cancelReply()
            $('.chat-container').empty();
            focusPlainTextarea();
            $('#plain-text').val('')
            if (response.tag_info) {
                const tagName = response.tag_info.tag_name;
                const tagColor = response.tag_info.color_code || '#FAFAFA';
            
                const badgeContainer = document.getElementById('bookmark_tag_badge');
                const badge = document.getElementById('bookmark_tag_badge');
            
                // Show badge container
                if (badgeContainer) badgeContainer.style.display = 'inline';
            
                // Set badge text and styling
                if (badge) {
                    badge.textContent = tagName;
                    badge.style.backgroundColor = tagColor;
                    badge.style.color = '#fff';
                    badge.style.padding = '2px 8px';
                    badge.style.borderRadius = '12px';
                    badge.style.fontSize = '12px';
                    badge.style.display = 'inline-block';
                }
            
            } else {
                // Hide badge if no tag info
                const badgeContainer = document.getElementById('bookmark_tag_badge');
                if (badgeContainer) badgeContainer.style.display = 'none';
            }
            
            $('#msgSlide').html('');
if (response.user_chat_note) {
    let htmlChat = '';
    response.user_chat_note.forEach(ucnData => {
        const dateObj = new Date(ucnData.created_at);

        const day = String(dateObj.getDate()).padStart(2, '0');
        const month = String(dateObj.getMonth() + 1).padStart(2, '0');
        const year = String(dateObj.getFullYear()).slice(-2); // last 2 digits
        const hours = String(dateObj.getHours()).padStart(2, '0');
        const minutes = String(dateObj.getMinutes()).padStart(2, '0');

        const formattedDate = `${day}-${month}-${year} ${hours}:${minutes}`;

        let note = ucnData.note || '';
        let formattedNote = note.length > 25
            ? note.slice(0, 25) + '<br>' + note.slice(25)
            : note;

        htmlChat += `
            <div class="note-entry">
                <div class="d-flex justify-content-between mb-1">
                    <strong>${ucnData.name}</strong>
                    <small class="text-muted">${formattedDate}</small>
                </div>
                <div>${formattedNote}</div>
            </div>
        `;
    });
    $('#msgSlide').html(htmlChat);
}

            

            
            // $('#agent_ids').val('').trigger('change');

            // if(response.agent_chat_assign){
            if (response.agent_chat_assign && response.agent_chat_assign.length > 0) {
                // console.log('vishal from if condition');
                
                window.skipAssign = true;
                const agentIds = response.agent_chat_assign.map(agtdata => agtdata.agent_id);
                $('#agent_ids').val(agentIds).trigger('change');
            }else {
                // console.log('vishal from else condition');
                $('#agent_ids').val('').trigger('change');
            }

            const blacklistIcon = document.getElementById('blacklist_tag_badge');
            if(response.blacklist_info){
                blacklistIcon.style.display = 'inline';

                blacklistIcon.textContent="Blocked";
                blacklistIcon.style.backgroundColor = '#FF0000';
                blacklistIcon.style.color = '#fff';
                blacklistIcon.style.padding = '2px 8px';
                blacklistIcon.style.borderRadius = '12px';
                blacklistIcon.style.fontSize = '12px';
                blacklistIcon.style.display = 'inline-block';

                $('#block_button_container').hide();
                $('#unblock_button_container').show();
            }else{
                blacklistIcon.style.display = 'none';

                $('#block_button_container').show();
                $('#unblock_button_container').hide();
            }
            
            let chats = sortByKey(response.chats, 'timestamp')
            let lastReceiveMessage = null;

            showLastMessageCountdown(chats); // show latest message countdown if have else show expired
            
            for (var i = 0; i < chats.length; i++) {
                const timestamp = chats[i]['timestamp']
                const statustype = {
                    0: 'Pending',
                    1: 'Sent',
                    2: 'Delivered',
                    3: 'Read',
                    4: 'Failed',
                }
                const task_status = chats[i]['task_status']
                // console.log('status',task_status);

                var taskDesctiption ='';
                if(task_status ==4){
                    taskDesctiption =chats[i]['task_description'];
                }else{
                    taskDesctiption ='';
                }
                const date = new Date(timestamp * 1000)
                const formatted_time = date.toLocaleTimeString('en-US', {
                    year: 'numeric',
                    day: 'numeric',
                    month: 'short',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: true,
                })
                //NOTE: This page css add into qr-page.css  file
                //get data from messages table Left side chat
                if (chats[i]['message_type'] === 'receive') {
                    var html = `<div class="message-box friend-message"><p style="font-size: 20px;text-align: center;">`
                    var media_ext_type = [
                        'jpg',
                        'jpeg',
                        'png',
                        'gif',
                        'svg', // Images
                        'mp4',
                        'mov',
                        'webm',
                        'avi', // Videos
                        'pdf',
                        'doc',
                        'docx',
                        'ppt',
                        'pptx',
                        'xls',
                        'xlsx',
                        'csv',
                        'txt', // Documents
                        'zip',
                        'rar',
                        '7z', // Archive Files
                        'mp3',
                        'wav',
                        'flac',
                        'ogg', // Audio Files
                    ]
                    let randomId = Math.floor(Math.random() * 10000); // Generate a random number
                    if (!!chats[i]['media']) {
                        
                        var file_type = chats[i]['type']
                        var media_url = chats[i]['media']
                        
                        if(file_type == 'image'){   
                            html += `<a href="${media_url}" target="_blank"><img src="${media_url}" class="img-fluid" style="width:180px; cursor: pointer;"></img><br>`;
                        }
                        if(file_type == 'video'){
                            html += `<video src="${media_url}" controls style="width:180px;"></video><br>`
                        }
                        if(file_type == 'document'){
                            html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/9496/9496432.png" style="width:180px;"></a><br>`
                        }
                    }
                    var escapedBodyRec = encodeURIComponent(chats[i]['body']);

                    html += `<span style="text-align: left;color:#000000;word-wrap: break-word;white-space: normal;">${formatWhatsAppTextToHTML(
                        chats[i]['body']
                    )}</span><br /><span class ="float-right" style="font-size: 12px;">${formatted_time}</span></p>
                    
                    <div class="dropdown msg-menu">
        
                    <a data-toggle="dropdown" aria-expanded="false"  class="three-dot-btn ml-2">
                    <svg viewBox="0 0 24 24" height="20" width="20" preserveAspectRatio="xMidYMid meet" class="" version="1.1" x="0px" y="0px" enable-background="new 0 0 24 24" style="border: 1px solid;border-radius: 50%;padding: 1px;margin-bottom: 50px;margin-right: 11px;cursor: pointer;">
                        <path d="M12,7c1.104,0,2-0.896,2-2c0-1.105-0.895-2-2-2c-1.104,0-2,0.894-2,2 C10,6.105,10.895,7,12,7z M12,9c-1.104,0-2,0.894-2,2c0,1.104,0.895,2,2,2c1.104,0,2-0.896,2-2C13.999,9.895,13.104,9,12,9z M12,15 c-1.104,0-2,0.894-2,2c0,1.104,0.895,2,2,2c1.104,0,2-0.896,2-2C13.999,15.894,13.104,15,12,15z">
                        </path>
                    </svg>
                </a>
                
        <div class="dropdown-menu dropdown-menu-right">
            <a class="dropdown-item" style="cursor:pointer;" onclick="replyToMessage('${chats[i]['message_id']}','${formatWhatsAppTextToHTML(chats[i]['body'])}')"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-reply" viewBox="0 0 16 16">
  <path d="M6.598 5.013a.144.144 0 0 1 .202.134V6.3a.5.5 0 0 0 .5.5c.667 0 2.013.005 3.3.822.984.624 1.99 1.76 2.595 3.876-1.02-.983-2.185-1.516-3.205-1.799a8.7 8.7 0 0 0-1.921-.306 7 7 0 0 0-.798.008h-.013l-.005.001h-.001L7.3 9.9l-.05-.498a.5.5 0 0 0-.45.498v1.153c0 .108-.11.176-.202.134L2.614 8.254l-.042-.028a.147.147 0 0 1 0-.252l.042-.028zM7.8 10.386q.103 0 .223.006c.434.02 1.034.086 1.7.271 1.326.368 2.896 1.202 3.94 3.08a.5.5 0 0 0 .933-.305c-.464-3.71-1.886-5.662-3.46-6.66-1.245-.79-2.527-.942-3.336-.971v-.66a1.144 1.144 0 0 0-1.767-.96l-3.994 2.94a1.147 1.147 0 0 0 0 1.946l3.994 2.94a1.144 1.144 0 0 0 1.767-.96z"/>
</svg> Reply</a>
            <a class="dropdown-item" style="cursor:pointer;" data-message="${escapedBodyRec}" onclick="copyMessage(this)"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-copy" viewBox="0 0 16 16">
  <path fill-rule="evenodd" d="M4 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2zm2-1a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1zM2 5a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-1h1v1a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h1v1z"/>
</svg> Copy</a>
            <a class="dropdown-item" style="cursor:pointer;" onclick="confirmDelete(${chats[i]['primary_id']}, 'receive')"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash" viewBox="0 0 16 16">
  <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z"/>
  <path d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4zM2.5 3h11V2h-11z"/>
</svg> Delete</a>
        </div>
    </div>
    
    </div>`
                    if (['jpg', 'jpeg', 'png', 'gif', 'svg'].includes(file_type)) {
                        html += `<div class="modal fade" id="zoomImageModal${randomId}" tabindex="-1" role="dialog" aria-labelledby="zoomImageModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-body">
                        <img src="${media_url}" class="img-fluid"></img>
                        </div>
                    </div>
                    </div>
                </div>`;}

                    $('.chat-container').append(html)
                } else {
                    //this else part data get from task table  Right side chat
                    let body = templateTobody(
                        response.templates,
                        chats[i]['templateId'],
                        chats[i]['parameters'],
                        chats[i]['text']
                    )
                    let bodyContent = '';
                    try {
                        let bodyObj = JSON.parse(body)
                        if (bodyObj && bodyObj.video && bodyObj.video.caption) {
                            bodyContent = bodyObj.video.caption
                        } else if (bodyObj && bodyObj.document && bodyObj.document.caption) {
                            bodyContent = bodyObj.document.caption
                        }else if (bodyObj && bodyObj.image && bodyObj.image.caption) {
                            bodyContent = bodyObj.image.caption
                        } else if (bodyObj && bodyObj.text) {
                            bodyContent = bodyObj.text.body
                        }
                    } catch (e) {
                        bodyContent = body;
                        // body is not a JSON string, do nothing
                    }
                    // console.log(body);
                    var escapedBody = encodeURIComponent(bodyContent ?? body);
                    var html = `<div class="message-box my-message">
                    
                    <div class="dropdown msg-menu">
                    
        

<a data-toggle="dropdown" aria-expanded="false"  class="three-dot-btn">
                    <svg viewBox="0 0 24 24" height="20" width="20" preserveAspectRatio="xMidYMid meet" class="" version="1.1" x="0px" y="0px" enable-background="new 0 0 24 24" style="border: 1px solid;border-radius: 50%;padding: 1px;margin-bottom: 50px;margin-right: 11px;cursor: pointer;">
                        <path d="M12,7c1.104,0,2-0.896,2-2c0-1.105-0.895-2-2-2c-1.104,0-2,0.894-2,2 C10,6.105,10.895,7,12,7z M12,9c-1.104,0-2,0.894-2,2c0,1.104,0.895,2,2,2c1.104,0,2-0.896,2-2C13.999,9.895,13.104,9,12,9z M12,15 c-1.104,0-2,0.894-2,2c0,1.104,0.895,2,2,2c1.104,0,2-0.896,2-2C13.999,15.894,13.104,15,12,15z">
                        </path>
                    </svg>
                </a>

        <div class="dropdown-menu dropdown-menu-right">
            <a class="dropdown-item" style="cursor:pointer;" data-message="${escapedBody}" onclick="copyMessage(this)"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-copy" viewBox="0 0 16 16">
  <path fill-rule="evenodd" d="M4 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2zm2-1a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1zM2 5a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-1h1v1a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h1v1z"/>
</svg> Copy</a>
            <a class="dropdown-item" style="cursor:pointer;" onclick="confirmDelete(${chats[i]['primary_id']}, 'send')"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash" viewBox="0 0 16 16">
  <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z"/>
  <path d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4zM2.5 3h11V2h-11z"/>
</svg> Delete</a>
        </div>
    </div>
    
    <p style="font-size: 20px;text-align: center;"><span style="text-align: left;margin-top=-10px !important;font-weight:bold;color:#0000FF;word-wrap: break-word;white-space: normal;">${chats[i]['user_name']}</span>`
    
                    var media_ext_type = [
                        'jpg',
                        'jpeg',
                        'png',
                        'gif',
                        'svg', // Images
                        'mp4',
                        'mov',
                        'webm',
                        'avi', // Videos
                        'pdf',
                        'doc',
                        'docx',
                        'ppt',
                        'pptx',
                        'xls',
                        'xlsx',
                        'csv',
                        'txt', // Documents
                        'zip',
                        'rar',
                        '7z', // Archive Files
                        'mp3',
                        'wav',
                        'flac',
                        'ogg', // Audio Files
                    ]
                    let randomId = Math.floor(Math.random() * 10000); // Generate a random number
                    if (!!chats[i]['media']) {
                        var media_url = chats[i]['media'];

                        var file_type = getExtension(media_url)

                        if (media_ext_type.includes(file_type)) {
                            if (['jpg', 'jpeg', 'png', 'gif', 'svg'].includes(file_type)) {

                                html += `<img src="${media_url}" class="img-fluid" style="width:180px; cursor: pointer;" data-toggle="modal" data-target="#zoomImageModal${randomId}"></img><br>`;
                            } else if (['mp4', 'mov', 'webm', 'avi'].includes(file_type)) {
                                html += `<video src="${media_url}" controls style="width:180px;"></video><br>`
                            } else if (
                                ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'csv'].includes(file_type)
                            ) {
                                if (file_type === 'pdf') {
                                    html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/9496/9496432.png" style="width:180px;"></a><br>`
                                } else if (file_type === 'doc' || file_type === 'docx') {
                                    html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/5968/5968517.png" style="width:180px;"></a><br>`
                                } else if (file_type === 'pptx') {
                                    html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/732/732224.png" style="width:180px;"></a><br>`
                                } else if (file_type === 'xls' || file_type === 'xlsx') {
                                    html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/4726/4726040.png" style="width:180px;"></a><br>`
                                } else if (file_type === 'csv') {
                                    html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/28/28842.png" style="width:180px;"></a><br>`
                                } else if (file_type === 'txt') {
                                    html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/337/337956.png" style="width:180px;"></a><br>`
                                }
                            } else if (['zip', 'rar', '7z'].includes(file_type)) {
                                if (file_type === 'zip') {
                                    html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/28/28814.png" style="width:180px;"></a><br>`
                                } else if (file_type === 'rar') {
                                    html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/28/28792.png" style="width:180px;"></a><br>`
                                } else if (file_type === '7z') {
                                    html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/29/29142.png" style="width:180px;"></a><br>`
                                }
                            } else if (['mp3', 'wav', 'flac', 'ogg'].includes(file_type)) {
                                if (file_type === 'mp3') {
                                    html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/81/81281.png" style="width:180px;"></a><br>`
                                } else if (file_type === 'wav') {
                                    html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/256/29/29101.png" style="width:180px;"></a><br>`
                                } else if (file_type === 'flac') {
                                    html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/8300/8300654.png" style="width:180px;"></a><br>`
                                } else if (file_type === 'ogg') {
                                    html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/9224/9224315.png" style="width:180px;"></a><br>`
                                }
                            }
                            else{
                                //other type of file show here
                                html += `<a href="${media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/2521/2521610.png" style="width:180px;"></a><br>`

                            }
                        }
                    }
                    html += `<span style="text-align: left;color:#000000;">${formatWhatsAppTextToHTML(
                        bodyContent ?? body
                    )}</span><br />
                    <span class ="float-right" style="font-size: 12px;" title="${taskDesctiption}">${formatted_time} (${
                        statustype[task_status]
                    })</span>
                    </p>
                    
                        <div class="dropdown msg-menu">
                            <a data-toggle="dropdown" aria-expanded="false" class="three-dot-btn" data-toggle="tooltip" title="${chats[i]['user_name']}">
                                <span class="avatar avatar-sm rounded-circle">
                                    <img alt="Image placeholder"
                                        src="${chats[i]['user_avatar'] ? chats[i]['user_avatar'] : 'https://ui-avatars.com/api/?name='+chats[i]['user_name']+'&background=0D8ABC&color=fff'}"
                                        class="avatar-img rounded-circle" style="width: 20px; height: 20px;">
                                </span>
                            </a>
                        </div>


    
                    </div>`
                    if (['jpg', 'jpeg', 'png', 'gif', 'svg'].includes(file_type)) {
                        html += ` <div class="modal fade" id="zoomImageModal${randomId}" tabindex="-1" role="dialog" aria-labelledby="zoomImageModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-body">
                        <img src="${media_url}" class="img-fluid"></img>
                        </div>
                    </div>
                    </div>
                </div>`;}
                    $('.chat-container').append(html)
                }
            }
        },
        error: function (xhr, status, error) {
            if (xhr.status == 500) {
            }
        },
    })
}

// Global timer reference
let countdownTimer = null;

function showLastMessageCountdown(chats) {
    // Clear previous timer and content
    if (countdownTimer) {
        clearInterval(countdownTimer);
        countdownTimer = null;
    }

    $('#remaining_timing').text(''); // Clear old display if any

    // Find the last "receive" message
    let lastReceiveMessage = null;
    for (let i = chats.length - 1; i >= 0; i--) {
        if (chats[i]['message_type'] === 'receive') {
            lastReceiveMessage = chats[i];
            //break;
        }
    }

    if (lastReceiveMessage) {
        const timestamp = lastReceiveMessage.timestamp;
        const receiveDate = new Date(timestamp * 1000);
        const deadline = timestamp + (24 * 3600); // 24 hours deadline

        function updateCountdown() {
            const nowSeconds = Math.floor(Date.now() / 1000);
            const remainingSeconds = deadline - nowSeconds;

            let remainTime = "";
            if (remainingSeconds > 0) {
                const hours = Math.floor(remainingSeconds / 3600);
                const minutes = Math.floor((remainingSeconds % 3600) / 60);
                remainTime = `${hours}:${minutes}`;
            } else {
                remainTime = "Expired";
                clearInterval(countdownTimer);
            }

            $('#remaining_timing').text(remainTime);
        }

        updateCountdown(); // First display
        countdownTimer = setInterval(updateCountdown, 1000);
    }
}

function replyToMessage(msgId,messageText){

    document.getElementById('reply-preview').classList.remove('d-none');

    document.getElementById('reply-sender').innerText = messageText;

    focusPlainTextarea();

    if(msgId){
        $('#rep_msg_id').val(msgId);
    }
    else{
        $('#rep_msg_id').val('');
    }
}

function focusPlainTextarea()
{
    const textarea = document.getElementById('plain-text');
    if (textarea) {
        textarea.focus();
    }
}



let deleteId = null;
let deleteType = null;

function confirmDelete(id, type) {
    deleteId = id;
    deleteType = type;
    $('#confirmDeleteModal').modal('show');
}

document.getElementById('confirmDeleteBtn').addEventListener('click', function () {
    $('#confirmDeleteModal').modal('hide');
    if (deleteId !== null && deleteType !== null) {
        deleteMessage(deleteId, deleteType); // your actual delete function
        deleteId = null;
        deleteType = null;
    }
});


    // Enable the tooltip
    $(document).ready(function() {
        $('[data-toggle="tooltip"]').tooltip();
    });

    function copyMessage(element) {
        // Get the encoded message from the data attribute
        const encodedMessage = element.getAttribute('data-message');
        const text = decodeURIComponent(encodedMessage);
        
        // Create a temporary textarea
        const textarea = document.createElement("textarea");
        textarea.value = text;
        document.body.appendChild(textarea);
    
        // Select and copy the text
        textarea.select();
        document.execCommand("copy");
    
        // Remove the textarea
        document.body.removeChild(textarea);
    
        // Optional: show a toast/alert
        // alert("Message copied!");
    }



function formatWhatsAppTextToHTML(text) {
    function escapeHTML(text) {
        const replacements = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#39;',
        }
        return text.replace(/[&<>"']/g, (char) => replacements[char])
    }

    // Replace newline characters with <br> tags
    text = text.replace(/\n/g, '<br>')

    // Convert asterisks to <b> tags for bold text
    text = text.replace(/\*(.*?)\*/g, '<b>$1</b>')

    // Convert underscores to <i> tags for italic text
    text = text.replace(/_(.*?)_/g, '<i>$1</i>')

    // Convert backticks to <code> tags for monospace text
    text = text.replace(/```(.*?)```/g, '<code>$1</code>')

    // Convert tildes to <strike> tags for strike-through text
    text = text.replace(/~~(.*?)~~/g, '<strike>$1</strike>')

    return text
}

$(document).on('change', '#select-type', function () {
    var type = $(this).val()

    if (type == 'plain-text') {
        $('#plain-text').show()
        $('#templates').hide()
        $('#mediapopup').show()
        $('#custom-send').show()
        $('#templatemodal').hide()
        // $('#medialists').hide()
    } else {
        $('#plain-text').hide()
        // $('#templates').show()
        $('#templates').hide()
        $('#mediapopup').hide()
        $('#custom-send').hide()
        $('#templatemodal').show()
        // $('#medialists').show()
    }
})

function sortByKey(array, key) {
    if (!Array.isArray(array)) {
        array = Object.values(array)
    }

    return array.sort(function (a, b) {
        var x = a[key]
        var y = b[key]
        return x > y ? -1 : x < y ? 1 : 0
    })
}

function formatTimestamp(unixTimestamp) {
   // Create a Date object from the timestamp
   let date = new Date(unixTimestamp);

   // Get today's date
   let today = new Date();
   today.setHours(0, 0, 0, 0); // Set hours, minutes, seconds, and milliseconds to 0 for comparison

   // Check if the date is today
   if (date.getTime() >= today.getTime()) {
       // If the date is today, return the time
       let hours = date.getHours().toString().padStart(2, '0');
       let minutes = date.getMinutes().toString().padStart(2, '0');
       return hours + ':' + minutes;
   } else {
       // If the date is not today, return the date
       let year = date.getFullYear();
       let month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are zero-based
       let day = date.getDate().toString().padStart(2, '0');
       return year + '-' + month + '-' + day;
   }
}

function templateTobody(templates, templateId, parameters, text) {
    var text_data
    var button_data

    for (var i = 0; i < templates.length; i++) {
        if (templates[i]['name'] == templateId) {
            var head_type = templates[i]['components'][0]['format'];
            // console.log('head',head_type);
            
            var tempComponentText =templates[i]['components'][1]['text'];
            var language = templates[i]['language']

            // Dynamically generate JavaScript code based on head_type
            if (head_type === 'IMAGE') {
                text_data = tempComponentText
            } else if (head_type === 'DOCUMENT') {
                // Generate code for DOCUMENT

                text_data = tempComponentText
                // console.log('document text',text_data);
                
            } else if (head_type === 'VIDEO') {
                // Generate code for VIDEO

                text_data = tempComponentText
            } else if (head_type === 'TEXT') {
                // Generate code for TEXT
                text_data = templates[i]['components'][0]['text'] + '\r\n' + tempComponentText
                if (parameters) {
                    var parameters_array = parameters.split('||')
                    const regex = /\{\{([^}]+)\}\}/g

                    // Replace all occurrences of `{{}}` with the corresponding values from `variables`.
                    text_data.replace(regex, (match, variable) => parameters_array[variable])
                }
            } else {
                // Handle other cases

                text_data = templates[i]['components'][0]['text']
            }

            if (head_type === 'IMAGE' || head_type === 'DOCUMENT'||head_type === 'VIDEO'||head_type === 'TEXT') {
            if (parameters) {
                text_data = tempComponentText
                
                var parameterss = parameters.split('||')
                const regex = /\{\{(\d+)\}\}/g;

                // Replace all occurrences of `{{}}` with the corresponding values from `variables`.
                text_data = text_data.replace(regex, (match, variable) => {
                    // Extract the index (e.g., 1 from {{1}})
                    const index = parseInt(variable) - 1; // Adjust for zero-based index in the array
                    return parameterss[index] ? `<u>${parameterss[index]}</u>` : match; // Replace with value or leave match if no value is found
                });
                // console.log('Updated text:', text_data);
            }}

            //TODO buttons add to ui
            // templates[i]['components'].forEach(function (item, index) {
            //     if (item['type'] == 'BUTTONS') {
            //         var variable_text = '<div class="form-group form-float">'
            //         item['buttons'].forEach(function (item, index) {
            //             // $("#temp_buttons").show();
            //             //delte all child elements of temp_params
            //             // $("#temp_buttons").empty();
            //             // console.log('button found');
            //             var button_text = item['text']
            //             var button_url = item['url']
            //             var button_phone_number = item['phone_number']
            //             var button_type = item['type']
            //             // console.log(button_type);
            //             if (button_type == 'URL') {
            //                 variable_text +=
            //                     '<a href="' +
            //                     button_url +
            //                     '" class="btn btn-sm btn-neutral btn-block" target="_blank"  margin-bottom:5px;" ><i class="fi fi-rs-arrow-up-right-from-square"></i>&nbsp;&nbsp;' +
            //                     button_text +
            //                     '</a>'
            //             } else if (button_type == 'PHONE_NUMBER') {
            //                 variable_text +=
            //                     '<a href="' +
            //                     button_phone_number +
            //                     '" class="btn btn-sm btn-neutral btn-block" target="_blank"  margin-bottom:5px;" ><i class="fi fi-rs-circle-phone-flip"></i>&nbsp;&nbsp;' +
            //                     button_text +
            //                     '</a>'
            //             }
            //             // $("#temp_buttons").append(variable_text);
            //         })
            //     } else {
            //         button_data = ''
            //         // $("#temp_buttons").hide();
            //     }
            // })
        }
    }
    if (text_data != null) {
        return text_data
    } else if (text != null) {
        return text
    } else {
        return ''
    }
}

function getFormattedTimestamp() {
    try {
      // Get current date and time in milliseconds
      const timestampInMilliseconds = Date.now();

      // Create a new Date object with the timestamp
      const date = new Date(timestampInMilliseconds);

      // Get the year, month (0-indexed), day, hours, minutes, and seconds
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0'); // Add leading zero for single-digit months
      const day = String(date.getDate()).padStart(2, '0'); // Add leading zero for single-digit days
      const hours = String(date.getHours()).padStart(2, '0'); // Add leading zero for single-digit hours
      const minutes = String(date.getMinutes()).padStart(2, '0'); // Add leading zero for single-digit minutes
      const seconds = String(date.getSeconds()).padStart(2, '0'); // Add leading zero for single-digit seconds

      // Format the timestamp string
      const formattedTimestamp = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

      return formattedTimestamp;
    } catch (error) {
      console.error("Error getting formatted timestamp:", error);
      return null; // Or return an error message or default value
    }
  }
