<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use App\Jobs\SendTaskJob;
use App\Models\User;
use App\Models\App;
use App\Models\Device;
use Illuminate\Support\Str;
use App\Traits\Whatsapp;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;

class RSPLController extends Controller
{
    use Whatsapp;
    public function rspl(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'appkey' => 'required',
            'authkey' => 'required',
            // 'token' => 'required',
            'reporttype' => 'required',
            // 'accounttype' => 'required',
            'accountid' => 'required',
            'to' => 'required',
            'text' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors(),
            ], 400);
        }

        // dd($request->all());
        $userPassword = substr($request->accountid, 0, 4) . substr($request->to, -4);
        $user = User::where('status', 1)->where('will_expire', '>', now())->where('authkey', $request->authkey)->first();
        $app = App::where('key', $request->appkey)->whereHas('device')->with('device')->where('status', 1)->first();

        $currentDate = Carbon::now()->format('Ymd');

        $currentDate_carbon = Carbon::now();
        $financialYearStart = ($currentDate_carbon->month >= 4)
            ? Carbon::create($currentDate_carbon->year, 4, 1)
            : Carbon::create($currentDate_carbon->year - 1, 4, 1)->format('Ymd');
        // dd($currentDate);

        if ($user == null || $app == null) {
            return response()->json(['error' => 'Invalid Auth and AppKey'], 401);
        }

        if (getUserPlanData('messages_limit', $user->id) == false) {
            return response()->json([
                'message' => __('Maximum Monthly Messages Limit Exceeded')
            ], 401);
        }

        $accountid = $request->accountid;

        if ($request->reporttype == 'Dp holding') {
            $token = $this->createToken();
            // dd('token is ',$token);
            $curl = curl_init();
            $xmlPayload = <<<XML
            <dsXml>
                <J_Ui>
                    "ActionName":"TradeWeb",
                    "Option":"HOLDING",
                    "Level":1,
                    "RequestFrom":"W"
                </J_Ui>
                <Sql></Sql>
                <X_Filter>
                    <FromDate>{$currentDate}</FromDate>
                    <Product>DP</Product>
                </X_Filter>
                <X_GFilter></X_GFilter>
                <J_Api>
                    "UserId":"{$accountid}"
                </J_Api>
            </dsXml>
            XML;
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://tradewebapi.ratnakarsecurities.com:8091/api/Main/TradeWeb',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $xmlPayload,
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/xml',
                    'Authorization: Bearer ' . $token,
                ),
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);
            // echo($response);

            if ($httpCode !== 200 || $curlError) {
                Log::error('RSPL API Request Failed', ['http_code' => $httpCode, 'error' => $curlError]);
                $rs0Data = [];
            } else {
                $holdings = json_decode($response, true);
                $rs0Data = $holdings['data']['rs0'] ?? [];
                array_multisort(array_column($rs0Data, 'Scrip Name'), SORT_ASC, $rs0Data);
            }

            if (empty($rs0Data)) {
                $bodyMessage = 'No record found for selected account';
                $filename = null;
                $pdfUrl = null;
            } else {
                $pdf = Pdf::loadView('user.rspl.holding_report', compact('rs0Data'));
                $pdf->setEncryption($userPassword, '', ['copy', 'print']);
                // return $pdf->stream('holding_report_' . time() . '.pdf'); // if use direct streaming
                $filename = 'holding_report_' . time() . '.pdf';
                $filePath = 'reports/dp_holding/' . $filename;
                Storage::disk('public')->put($filePath, $pdf->output());
                $pdfUrl = asset($filePath);
                $bodyMessage = $request->text;
            }

            $replay =  $this->waba_json_generate($bodyMessage, $pdfUrl, null, null, 'Dp Holding - ' . $accountid);
            $uuid = (string) Str::uuid();
            $data = [
                'device_id' => $app->device_id,
                'created_by' => $app->user_id,
                'scheduled_on' => now(),
                'send_to_number' => $request->to,
                'templateId' => 'auto_reply',
                'is_reply' => 1,
                'text' => $replay,
                'task_type' => 3,
                'parameters' => null,
                'task_url' => $pdfUrl ?? null,
                'ip' => $request->getClientIp(),
                'whatsapp_id' => $uuid,
                'campaign_name' => 'Dp holding - ' . $accountid,
                'created_at' => now(),
                'updated_at' => now()
            ];
            try {
                DB::table('task')->insert($data);
                SendTaskJob::dispatch($uuid)->onQueue('high');
            } catch (\Exception $e) {
                return response()->json(['error' => 'Request Failed ' . $e], 401);
            }

            return response()->json([
                'message_status' => 'Success',
                'data' => [
                    'device_phone' => $app->device->phone ?? null,
                    'to' => $request->to,
                    'media' => $pdfUrl,
                    'message' => $bodyMessage,
                    'status_code' => 200,
                ]
            ], 200);
        } elseif ($request->reporttype == 'Ledger Statement') {
            $token = $this->createToken();

            $curl = curl_init();

            $xmlPayload = <<<XML
            <dsXml>
                    <J_Ui>"ActionName":"TradeWeb", "Option":"LEDGER","Level":1, "RequestFrom":"W"</J_Ui>
                    <Sql></Sql>
                    <X_Filter>
                        <FromDate>{$financialYearStart}</FromDate>
                        <ToDate>{$currentDate}</ToDate>
                        <ReportType>Trading</ReportType>
                        <IncludeMTF>Y</IncludeMTF>
                    </X_Filter>
                    <X_GFilter></X_GFilter>
                    <J_Api>"UserId":"{$accountid}"</J_Api>
                </dsXml>
            XML;

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://tradewebapi.ratnakarsecurities.com:8091/api/Main/TradeWeb',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $xmlPayload,
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/xml',
                    'Authorization: Bearer ' . $token,
                ),
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);
            //echo $response;

            // echo($response);

            $reportHeader = null;
            $nameData = null;
            if ($httpCode !== 200 || $curlError) {
                Log::error('RSPL API Request Failed', ['http_code' => $httpCode, 'error' => $curlError]);
                $ledger = [];
            } else {
                $resData = json_decode($response, true);
                // dd($ledger);
                $ledger = array_reverse($resData['data']['rs0']) ?? [];
                $acDetail = $resData['data']['rs1'][0]['Settings'] ?? [];
                // if($acDetail)
                // $settingsXml = $ledger['data']['rs1'] ?? '';

                if (!empty($acDetail)) {
                    // Load XML data
                    $xml = simplexml_load_string($acDetail, "SimpleXMLElement", LIBXML_NOCDATA);

                    // Convert XML to JSON, then to array
                    $settingsArray = json_decode(json_encode($xml), true);

                    // dd($settingsArray); // Check parsed data
                    $reportHeader = $settingsArray['ReportHeader'];
                    $reportHeader = trim($reportHeader);

                    // Check if the delimiter "-" exists in the string before exploding
                    if (strpos($reportHeader, '-') !== false) {
                        $expData = explode('-', $reportHeader)[1];
                        $nameData = explode('\n', $expData);
                    } else {
                        $expData = [$reportHeader]; // Return the full string as an array if "-" is not found
                    }
                    // dd($nameData);
                    // {!! nl2br(e($settingsArray['ReportHeader'])) !!}
                    // dd($reportHeader);
                } else {
                    $reportHeader = null;
                }

                // dd($acDetail);
            }

            if (empty($ledger)) {
                $bodyMessage = 'No record found for selected account';
                $filename = null;
                $pdfUrl = null;
            } else {
                $pdf = Pdf::loadView('user.rspl.ledger_report', compact('nameData', 'expData', 'reportHeader', 'ledger'));
                // return $pdf->stream('ledger_report_' . time() . '.pdf'); // if use direct streaming
                $pdf->setEncryption($userPassword, '', ['copy', 'print']);
                $filename = 'ledger_report_' . time() . '.pdf';
                $filePath = 'reports/ledger/' . $filename;
                Storage::disk('public')->put($filePath, $pdf->output());
                $pdfUrl = asset($filePath);
                $bodyMessage = $request->text;
            }

            $replay =  $this->waba_json_generate($bodyMessage, $pdfUrl, null, null, 'Ledger Statement - ' . $accountid);
            $uuid = (string) Str::uuid();
            $data = [
                'device_id' => $app->device_id,
                'created_by' => $app->user_id,
                'scheduled_on' => now(),
                'send_to_number' => $request->to,
                'templateId' => 'auto_reply',
                'is_reply' => 1,
                'text' => $replay,
                'task_type' => 3,
                'parameters' => null,
                'task_url' => $pdfUrl ?? null,
                'ip' => $request->getClientIp(),
                'whatsapp_id' => $uuid,
                'campaign_name' => 'Ledger Statement - ' . $accountid,
                'created_at' => now(),
                'updated_at' => now()
            ];
            try {
                DB::table('task')->insert($data);
                SendTaskJob::dispatch($uuid)->onQueue('high');
            } catch (\Exception $e) {
                return response()->json(['error' => 'Request Failed ' . $e], 401);
            }

            return response()->json([
                'message_status' => 'Success',
                'data' => [
                    'device_phone' => $app->device->phone ?? null,
                    'to' => $request->to,
                    'media' => $pdfUrl,
                    'message' => $bodyMessage,
                    'status_code' => 200,
                ]
            ], 200);
        } elseif ($request->reporttype == 'Outstanding position') {
            $token = $this->createToken();

            $curl = curl_init();

            $xmlPayload = <<<XML
            <dsXml>
            <J_Ui>"ActionName":"TradeWeb", "Option":"OSPosition","Level":1,"RequestFrom" :"W"</J_Ui>
                    <Sql></Sql>
                    <X_Filter>
                        <FromDate>{$currentDate}</FromDate>
                        <Segment>DERV</Segment>
                    </X_Filter>
                    <X_GFilter></X_GFilter>
                    <J_Api>"UserId":"{$accountid}"</J_Api>
                </dsXml>
            XML;

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://tradewebapi.ratnakarsecurities.com:8091/api/Main/TradeWeb',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $xmlPayload,
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/xml',
                    'Authorization: Bearer ' . $token,
                ),
            ));

            $response = curl_exec($curl);


            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);
            // echo($response);

            if ($httpCode !== 200 || $curlError) {
                Log::error('RSPL API Request Failed', ['http_code' => $httpCode, 'error' => $curlError]);
                $outstanding = [];
            } else {
                $outstanding = json_decode($response, true);
                $outstanding = $outstanding['data']['rs0'] ?? [];
            }

            if (empty($outstanding)) {
                $bodyMessage = 'No record found for selected account';
                $filename = null;
                $pdfUrl = null;
            } else {
                $customPaper = array(0, 0, 1060, 1840);
                $pdf = Pdf::loadView('user.rspl.outstanding_report', compact('outstanding'));
                $pdf->setEncryption($userPassword, '', ['copy', 'print']);
                // return $pdf->stream('outstanding_report_' . time() . '.pdf'); // if use direct streaming
                $filename = 'outstanding_report_' . time() . '.pdf';
                $filePath = 'reports/outstanding/' . $filename;
                // Storage::disk('public')->pu
                Storage::disk('public')->put($filePath, $pdf->output());
                $pdfUrl = asset($filePath);
                $bodyMessage = $request->text;
            }

            $replay =  $this->waba_json_generate($bodyMessage, $pdfUrl, null, null, 'Outstanding Position - ' . $accountid);
            $uuid = (string) Str::uuid();
            $data = [
                'device_id' => $app->device_id,
                'created_by' => $app->user_id,
                'scheduled_on' => now(),
                'send_to_number' => $request->to,
                'templateId' => 'auto_reply',
                'is_reply' => 1,
                'text' => $replay,
                'task_type' => 3,
                'parameters' => null,
                'task_url' => $pdfUrl ?? null,
                'ip' => $request->getClientIp(),
                'whatsapp_id' => $uuid,
                'campaign_name' => 'Outstanding position - ' . $accountid,
                'created_at' => now(),
                'updated_at' => now()
            ];
            try {
                DB::table('task')->insert($data);
                SendTaskJob::dispatch($uuid)->onQueue('high');
            } catch (\Exception $e) {
                return response()->json(['error' => 'Request Failed ' . $e], 401);
            }

            return response()->json([
                'message_status' => 'Success',
                'data' => [
                    'device_phone' => $app->device->phone ?? null,
                    'to' => $request->to,
                    'media' => $pdfUrl,
                    'message' => $bodyMessage,
                    'status_code' => 200,
                ]
            ], 200);
        } else {
            return response()->json([
                'message_status' => 'Failed',
                'data' => [
                    'message' => 'Invalid Report Type',
                    'status_code' => 400,
                ]
            ], 400);
        }
    }

    public function createToken()
    {
        $url = "https://tradewebapi.ratnakarsecurities.com:8091/api/Main/Login_validate_Password?userId=RSPLAPI&password=Pavan%4099&loginAs=B&product=%23";

        $response = Http::withHeaders([
            // 'Authorization' => "Bearer {$token}",
        ])->post($url);

        // curl_close($curl);
        // echo $response;
        $response = json_decode($response);
        $token = $response->token;
        // dd('resp',$response->token);
        // Log::info(($response);
        return $token;
    }
}
