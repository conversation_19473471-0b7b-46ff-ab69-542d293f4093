<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Jobs\BulkInsertTaskJob;

class ScheduleGreetings extends Command
{
    protected $signature = 'schedule:greetings';
    protected $description = 'Schedule greetings for contacts';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->scheduleGreetings();
    }

    public function scheduleGreetings()
    {
        $this->scheduleEvents("birthday_date", "birth_day_temp", "birth_day_media", "b_media_type", "b_lang");
        $this->scheduleEvents("anniversary_date", "anni_day_temp", "anni_day_media", "a_media_type", "a_lang");
    }

    public function scheduleEvents($dateField, $templateField, $mediaField, $mediaTypeField, $langField)
    {
        $currentDate = Carbon::now()->format('m-d');

        $results = DB::table('contacts')
            ->join('greetings', 'contacts.user_id', '=', 'greetings.user_id')
            ->where(function ($query) use ($dateField, $currentDate) {
                if (DB::connection()->getDriverName() === 'mysql') {
                    $query->where(DB::raw("DATE_FORMAT(contacts.$dateField, '%m-%d')"), $currentDate);
                } else {
                    // Assume PostgreSQL or other database with to_char() function
                    $query->where(DB::raw("to_char(contacts.$dateField, 'MM-DD')"), $currentDate);
                }
            })
            ->select('contacts.name', 'contacts.phone', 'contacts.anniversary_date', 'contacts.birthday_date', 'contacts.user_id', "greetings.device_id as deviceId", "greetings.$templateField as templateId", "greetings.$mediaTypeField as task_type", "greetings.$mediaField as task_url")
            ->get();

        if ($results->count() > 0) {
            foreach ($results as $event) {
                $this->createGreetingTask($event);
            }
        }
    }

    public function createGreetingTask($event)
    {
        $ip = request()->ip();
        $launch_time = now();
        $mediaUrl = $event->task_url ? asset("uploads/greetings/{$event->task_url}") : null;
        $taskdata = [
            'device_id' => $event->deviceId,
            'created_by' => $event->user_id,
            'scheduled_on' => $launch_time,
            'task_url' => $mediaUrl,
            'templateId' => $event->templateId,
            'task_type' => $event->task_type,
            'parameters' => null,
            'text' => null,
            'ip' => $ip,
            'created_at' => now(),
            'updated_at' => now()
        ];

        try {
            dispatch(new BulkInsertTaskJob($taskdata, array($event->phone)));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error dispatching the job: ' . $e->getMessage());
        }

        $this->info('Task created');
    }
}
