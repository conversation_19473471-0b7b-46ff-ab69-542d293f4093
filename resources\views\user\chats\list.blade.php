@extends('layouts.main.app')
@push('css')
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/qr-page.css') }}">
@endpush
@push('topcss')
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/vendor/select2/dist/css/select2.min.css') }}">
@endpush
@section('content')
    <style>
        #loader {
            position: absolute;
            left: 50%;
            top: 50%;
            z-index: 1;
            width: 50px;
            height: 50px;
            margin: -76px 0 0 -76px;
            border: 10px solid #f3f3f3;
            border-radius: 50%;
            border-top: 10px solid #3498db;
            -webkit-animation: spin 2s linear infinite;
            animation: spin 2s linear infinite;
        }

        @-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Add animation to "page content" */
        .animate-bottom {
            position: relative;
            -webkit-animation-name: animatebottom;
            -webkit-animation-duration: 1s;
            animation-name: animatebottom;
            animation-duration: 1s
        }

        @-webkit-keyframes animatebottom {
            from {
                bottom: -100px;
                opacity: 0
            }

            to {
                bottom: 0px;
                opacity: 1
            }
        }

        @keyframes animatebottom {
            from {
                bottom: -100px;
                opacity: 0
            }

            to {
                bottom: 0;
                opacity: 1
            }
        }

        .main-container {
            margin-top: 72px;
        }

        input[type="color"] {
            -webkit-appearance: none;
            width: 30px;
            height: 30px;
            border: 0;
            border-radius: 50%;
            padding: 0;
            overflow: hidden;
            box-shadow: 2px 2px 5px rgba(0, 0, 0, .1);
        }

        input[type="color"]::-webkit-color-swatch-wrapper {
            padding: 0;
        }

        input[type="color"]::-webkit-color-swatch {
            border: none;
        }

        .chat-container {
            position: relative;
            height: 550px;
            /* overflow-x: hidden; */
            /* overflow-y: scroll; */
            overflow: auto;
            display: flex;
            flex-direction: column-reverse;
        }

        .chat-container::-webkit-scrollbar {
            display: none;
        }

        .centered-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            cursor: pointer;
        }

        /* + sign css  */
        #plussign {
            background-color: #058D27;
            margin-right: 5px;
            margin-bottom: 5px;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 20px;
            cursor: pointer;
        }

        @media (max-width: 767px) {
            /* Adjust breakpoint as needed */

            /* Hide dropdown elements */
            select#select-type {
                display: none;
            }

            /* Hide select media button */
            #plussign {
                display: none;
            }

            /* Optional: Adjust textarea width for better mobile fit */
            #plain-text {
                flex: 0 0 77% !important;
                /* Expand to full width */
            }
        }

        .slide-message {
            position: fixed;
            top: 50%;
            right: -400px;
            transform: translateY(-50%);
            background-color: #ffffff;
            color: rgb(0, 0, 0);
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
            transition: right 0.5s ease-in-out;
            z-index: 999;
        }

        .slide-message.active {
            right: 20px;
            /* slide in to this position */
        }

        .modal.right.fade.in .modal-dialog {
            right: 0 !important;
            transform: translateX(-50%);
        }

        .modal.right .modal-content {
            height: 100%;
            overflow: auto;
            border-radius: 0;
        }

        .modal.right .modal-dialog {
            position: fixed;
            margin: auto;
            height: 100%;
            -webkit-transform: translate3d(0%, 0, 0);
            -ms-transform: translate3d(0%, 0, 0);
            -o-transform: translate3d(0%, 0, 0);
            transform: translate3d(0%, 0, 0);
        }

        .modal.right.fade.in .modal-dialog {
            transform: translateX(0%);
        }

        .modal.right.fade .modal-dialog {
            right: -50px;
            -webkit-transition: opacity 0.3s linear, right 0.3s ease-out;
            -moz-transition: opacity 0.3s linear, right 0.3s ease-out;
            -o-transition: opacity 0.3s linear, right 0.3s ease-out;
            transition: opacity 0.3s linear, right 0.3s ease-out;
        }

        /* Overlay */
        #modalOverlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            background-color: rgba(0, 0, 0, 0.3);
            z-index: 999;
        }

        #modalOverlay.show {
            display: block;
        }

        /* Modal */
        #rightModal {
            position: fixed;
            top: 0;
            right: -400px;
            /* hidden by default */
            width: 350px;
            height: 100vh;
            background-color: #fdfdfd;
            /* background: #fff; */
            box-shadow: -3px 0 8px rgba(0, 0, 0, 0.2);
            /* padding: 15px; */
            padding: 20px 15px;
            display: flex;
            flex-direction: column;
            z-index: 1000;
            transition: right 0.3s ease-in-out;
            overflow: hidden;
        }

        #rightModal.show {
            right: 0;
        }

        /* Scrollable content */
        .modalContent {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 10px;
            padding-right: 5px;
        }

        #msgSlide {
            flex: 1;
            overflow-y: auto;
            padding-right: 5px;
            max-height: calc(100vh - 210px);
            /* Adjust depending on textarea height */
            font-size: 14px;
        }

        .note-entry {
            margin-bottom: 12px;
            padding: 10px;
            background-color: #f1f5f9;
            border-radius: 8px;
            line-height: 1.4;
        }

        .note-entry strong {
            color: #007bff;
        }

        .note-entry {
            background-color: #f8f9fa;
            padding: 10px 12px;
            border-radius: 6px;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .note-entry time {
            font-size: 12px;
            color: #888;
            display: block;
            margin-bottom: 4px;
        }

        #noteInputArea {
            background: #fff;
        }

        .reply-preview-box {
            border-radius: 5px;
            margin: 5px 0;
            position: relative;
        }

        .reply-preview-box button {
            cursor: pointer;
            font-size: 20px;
            color: #ff5252;
        }

        #plain-text::placeholder {
            color: #888;
            font-style: italic;
        }
    </style>
    <div class="main-container row">
        @if (getUserPlanData('access_chat_list') == true)
            <div class="col-sm-4">
                <div class="form-group row">
                    <div class="col-md-12">
                        <input type="text" data-target=".contact" class="form-control consearch" id="consearch"
                            name="consearch" placeholder="{{ __('Search....') }}">
                        <a class="nav-link centered-icon float-right" role="button" data-toggle="modal"
                            data-target="#search-status-tag" style="margin-top: -40px;margin-right: 10px">
                            <i class="fi fi-rs-filter"></i>
                        </a>
                    </div>
                </div>
                <div class="clearfix">
                    <div id="loader" class="qr-area"></div>

                </div>
                <div class="alert bg-gradient-red server_disconnect none text-white" role="alert">
                    {{ __('Opps! Server Disconnected 😭') }}
                </div>
                <ul class="list-group list-group-flush list my--3 contact-list mt-5 position-relative">
                </ul>
            </div>
            <div class="col-sm-8 back-image">
                <nav class="navbar navbar-light" style=" background: rgb(250, 250, 250);">
                    <span class="avatar rounded-circle wa-link ml-2" class="navbar-brand mb-0 h1"><img class="img-fluid"
                            src="{{ asset('assets/img/whatsapp.png') }}" alt="user img"></span>
                    {{-- <div class="flex-grow-1 ms-3"> --}}
                    <div class="flex-grow-1 ms-3" style="display: flex; flex-direction: column;">
                        <div style="display: flex; align-items: center;">
                            <input type="number" readonly="" id="tagrecivernumber" name="tagrecivernumber"
                                value="" class="reciver-number"
                                style="background: transparent; border: none;margin-left: 15px;margin-right:-80px;font-size: large;"
                                hidden>

                            <input type="text" readonly="" id="tagrecivername" name="tagrecivername" value=""
                                class="reciver-name"
                                style="background: transparent; border: none;margin-left: 15px;margin-right:-80px;font-size: large;outline: none;">

                            {{-- bookmark tag --}}
                            {{-- <svg width="30" height="30" viewBox="0 0 30 30" id="bookmark_tag">
                                <path d="M17 3H7c-1.1 0-2 .9-2 2v18l7-7 7 7V5c0-1.1-.9-2-2-2" fill="#FAFAFA" />
                            </svg> --}}

                            {{-- blacklist contact tag icon --}}
                            {{-- <svg width="24" height="24" viewBox="0 0 24 24" id="blacklist_tag">
                                <circle cx="12" cy="12" r="10" stroke="#FAFAFA" stroke-width="2"
                                    fill="none" />
                                <line x1="4" y1="4" x2="20" y2="20" stroke="#FAFAFA"
                                    stroke-width="2" />
                            </svg> --}}
                        </div>

                        <span style="font-size: small; color: gray; margin-left: 15px; margin-top: 3px;">
                            <span class="badge" id="bookmark_tag_badge" style="display:none;"></span>
                            <span class="badge" id="blacklist_tag_badge" style="display:none;"> </span>
                        </span>
                    </div>
                    {{-- below li use for create a new tag and add a tag into contact --}}
                    <li class="nav-item dropdown" id="tagpopup" style="display: flex; align-items: center;">
                        <a style="margin-right: 8px; cursor:pointer;">
                            {{--  data-toggle="tooltip" data-placement="top" title="Remaining time" --}}
                            {{-- <p style="color: red;" id="remaining_timing"></p> --}}
                            {{-- <i class="fas fi-rs-call-outgoing"
                                style="border: 1px solid; border-radius: 50%; padding: 4px;"></i> --}}
                        </a>
                        <a onclick="showMessage()" style="margin-right: 8px; cursor:pointer;">
                            <i class="fas fi-rs-notebook" style="border: 1px solid; border-radius: 50%; padding: 4px;"></i>
                        </a>

                        <!-- save number to contact book icon -->
                        <a onclick="saveContact()" style="margin-right: 8px; cursor:pointer;">
                            <i class="fas fi-rs-user-add" style="border: 1px solid; border-radius: 50%; padding: 4px;"></i>
                        </a>

                        <!-- dial number icon -->
                        <a onclick="clickToDialNumber()" style="margin-right: 8px; cursor:pointer;">
                            <i class="fas fi-rs-call-outgoing"
                                style="border: 1px solid; border-radius: 50%; padding: 4px;"></i>
                        </a>

                        <!-- manage tag icon -->
                        <a data-toggle="dropdown" aria-expanded="false" data-animation="scale-up" role="button">
                            <svg viewBox="0 0 24 24" height="24" width="24" preserveAspectRatio="xMidYMid meet"
                                style="border: 1px solid; border-radius: 50%; padding: 1px; cursor:pointer;">
                                <path
                                    d="M12,7c1.104,0,2-0.896,2-2c0-1.105-0.895-2-2-2c-1.104,0-2,0.894-2,2 C10,6.105,10.895,7,12,7z M12,9c-1.104,0-2,0.894-2,2c0,1.104,0.895,2,2,2c1.104,0,2-0.896,2-2C13.999,9.895,13.104,9,12,9z M12,15 c-1.104,0-2,0.894-2,2c0,1.104,0.895,2,2,2c1.104,0,2-0.896,2-2C13.999,15.894,13.104,15,12,15z">
                                </path>
                            </svg>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right dropdown-menu-media" role="menu">
                            <div class="list-group">
                                <div data-role="container">
                                    <div data-role="content">
                                        <a class="list-group-item dropdown-item" role="tagname">
                                            <div class="tag_selection d-flex align-items-center justify-content-between"
                                                style="gap: 10px;">
                                                <div class="form-group mb-0">
                                                    <select class="form-control" id="tag_select" name="tag_id"
                                                        style="width: 200px" onchange="updateTagAndClose()"
                                                        onclick="preventPropagation(event)">
                                                        <option disabled selected>Select tag</option>
                                                        @foreach ($tag_name as $tag)
                                                            <option value="{{ $tag->id }}">
                                                                {{ $tag->tag_name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="form-group mb-0">
                                                    <button class="btn btn-outline-primary submit-button"
                                                        data-toggle="modal" data-target="#create-new-tag"
                                                        id="create-new-tags"><i class="fas fa-plus"></i></button>
                                                </div>
                                            </div>
                                        </a>
                                        <div class="list-group-item dropdown-item" onclick="preventPropagation(event)">
                                            {{-- <div> --}}
                                            <label for="agent_ids">Select Agents</label>
                                            <div class="form-group">
                                                <select class="form-control assign_agents" id="agent_ids"
                                                    name="agent_ids[]" multiple="" onchange="assignChatAndClose()">
                                                    <option value="" disabled>{{ __('-- Select Agent --') }}</option>
                                                    @foreach ($agentData as $agnt)
                                                        <option value="{{ $agnt['id'] }}">{{ $agnt['name'] }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            {{-- </div> --}}
                                        </div>
                                        <a class="list-group-item dropdown-item" id="block_button_container"
                                            style="display: none;">
                                            <div>
                                                <div class="form-group">
                                                    <button onclick="blockNumber()"
                                                        class="btn btn-outline-danger submit-button btn-block"
                                                        id="block_number">
                                                        {{ __('Block Number') }}
                                                    </button>
                                                </div>
                                            </div>
                                        </a>
                                        <a class="list-group-item dropdown-item" id="unblock_button_container"
                                            style="display: none;">
                                            <div>
                                                <div class="form-group">
                                                    <button onclick="unblockNumber()"
                                                        class="btn btn-outline-primary submit-button btn-block"
                                                        id="unblock_number">
                                                        {{ __('Unblock Number') }}
                                                    </button>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                </nav>
                <div id="modalOverlay" onclick="closeModal()"></div>
                <!-- Modal -->


                <div id="rightModal">
                    {{-- <h4 class="mb-3 border-bottom pb-2">Notes</h4> --}}
                    <div class="d-flex justify-content-between align-items-center mb-3 border-bottom pb-2">
                        <h4 class="mb-0">Notes</h4>
                        {{-- <button onclick="closeModal()" class="btn btn-sm btn-light border">
                            &times;
                        </button> --}}
                        <button type="button" onclick="closeModal()" class="close" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>

                    <!-- Scrollable dynamic content -->
                    <div class="modalContent mb-2" id="msgSlide">
                        <!-- Dynamic notes will be injected here -->
                    </div>

                    <!-- Fixed bottom note input -->
                    <div id="noteInputArea" class="pt-2 border-top">
                        <label for="noteInput" class="form-label">Add Note:</label>
                        <textarea id="noteInput" class="form-control mb-2" rows="2" placeholder="Write a note..."></textarea>
                        <input type="hidden" name="rep_device_id" id="rep_device_id" value="{{ $device['id'] }}">
                        <button onclick="saveNoteMsg()" class="btn btn-outline-primary w-100"
                            type="submit">Save</button>
                    </div>
                </div>

                <!--chat-container -->
                <form method="post" id="message-form" class="ajaxform"
                    action="{{ route('user.chat.send-message', $device->uuid) }}">
                    @csrf
                    <input type="number" readonly="" id="reciver" name="reciver" value=""
                        class="form-control reciver-number" style="background: transparent; border: none;" hidden>
                    <div class="chat-container" id="chatContainer" style="max-height: auto; overflow-y: auto;">

                    </div>
                    <div class="input-group none sendble-row wa-content-area flex-column"
                        style="position: fixed;width:-webkit-fill-available;background: #FAFAFA;">

                        <div id="reply-preview" class="reply-preview-box w-100 px-3 py-2 mb-0 d-none"
                            style="flex: 0 0 100%; padding: 10px; background: #e0f7fa; border-left: 4px solid #00bcd4; position: relative;">
                            <strong id="reply-sender" style="color:#00796b;"></strong>
                            <button type="button" onclick="cancelReply()"
                                style="position: absolute; right: 15px; top: 5px; background: none; border: none; font-weight: bold;">×</button>
                        </div>

                        <div class="d-flex w-100 align-items-center px-2">

                            <select class="form-control" name="selecttype" id="select-type"
                                style="flex: 0 0 20% !important;">
                                <option value="plain-text">{{ __('Custom Reply') }}</option>
                                {{-- <option value="template">{{ __('Template') }}</option> --}}
                            </select>

                            {{-- <textarea name="message" id="plain-text" style="flex: 0 0 60%;" rows="1"></textarea> --}}
                            <input type="hidden" id="rep_msg_id" name="rep_msg_id">

                            <textarea id="plain-text" name="message" class="form-control" rows="2"
                                placeholder="Type your message here... (Ctrl+Enter or Cmd+Enter to send)"></textarea>

                            {{-- <button type="button" id="record-button" class="btn btn-outline-primary btn-sm mt-2 ml-1">
                            <i class="fi-rs-microphone"></i></button> --}}

                            {{-- media selection list --}}
                            <li class="nav-item dropdown" id="mediapopup">
                                <a class="btn btn-outline-primary btn-sm mt-2 ml-1" id="plussign" data-toggle="dropdown"
                                    aria-expanded="false" data-animation="scale-up" role="button"
                                    onclick="open_file()"><i class="fa fa-paperclip" aria-hidden="true"></i></a>
                                <div class="dropdown-menu dropdown-menu-right dropdown-menu-media" role="menu" hidden>
                                    <div class="list-group">
                                        <div data-role="container">
                                            <div data-role="content">
                                                <a class="list-group-item dropdown-item" role="menuitem">
                                                    <div class="media">
                                                        <div class="pr-10 mr-2">
                                                            <i class="fas fa-upload" aria-hidden="true"></i>
                                                        </div>
                                                        <div class="form-group">
                                                            <label class="ml-1">{{ __('Select Media') }}</label>
                                                            <input type="file" name="mediafile" id="mediafile"
                                                                class="form-control" hidden>
                                                        </div>
                                                    </div>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            {{-- select template --}}
                            <div class="nav-item dropdown" id="templatemodal" style="display: none;">
                                <a class="btn btn-outline-primary btn-sm mt-2 ml-1" id="plussign" data-toggle="modal"
                                    data-target="#tempselect" role="button">•••</a>
                            </div>
                            <div class="input-group-append" id="custom-send">
                                <button class="btn btn-success  mr-3 submit-button" type="submit"><i
                                        class="fi fi-rs-paper-plane"></i>&nbsp;
                                </button>
                            </div>
                        </div>
                    </div>
                    <audio id="audio" controls hidden></audio>
                    <input type="hidden" name="voice_message" id="voice-message">
                </form>
            </div>
        @else
            <div class="col-sm-12">
                <div class="alert bg-gradient-primary text-white alert-dismissible fade show" role="alert">
                    <span class="alert-icon"><i class="fi  fi-rs-info text-white"></i></span>
                    <span class="alert-text">
                        <strong>{{ __('!Opps ') }}</strong>

                        {{ __('Chat list access features is not available in your subscription plan') }}

                    </span>
                </div>
            </div>
        @endif
    </div>

    {{-- select template with media and parameter starts --}}
    <div class="modal fade" id="tempselect" tabindex="-1" role="dialog" aria-labelledby="tempselectlabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form method="post" id="template-form" class="ajaxform"
                    action="{{ route('user.chat.send-message', $device->uuid) }}" enctype="multipart/form-data">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">{{ __('Template') }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <input type="text" name="campaign_devicetemp" id="campaign_devicetemp"
                            value="{{ $device['id'] }}" hidden>
                        <input type="text" name="selecttype" id="selecttype" value="template" hidden>
                        <input type="number" id="reciver" name="reciver" value=""
                            class="form-control reciver-number" hidden>
                        <div class="form-group">
                            <label>{{ __('Select Template') }}</label>
                            <select class="form-control" name="templateselection" id="templateselection"
                                data-toggle="select">
                                <option value="null">{{ __('-- Select Template --') }}</option>
                                @foreach ($templates_data as $templates)
                                    <option value="{{ $templates['name'] }}">{{ $templates['name'] }}
                                        ({{ $templates['language'] }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group" id="templates_head_text" style="display: none;">
                            <label></label>
                            <input type="textarea" id="temphead_text" name="temphead_text" class="form-control"
                                placeholder="temphead_text" disabled>
                        </div>
                        <div class="form-group" id="temp_langue" style="display: none;">
                            <label></label>
                            <input type="text" id="temp_language" name="temp_language" class="form-control"
                                placeholder="temp_language">
                        </div>
                        <div class="form-group" id="tsktmp_type" style="display: none;">
                            <label></label>
                            <input type="text" id="tasktemp_type" name="tasktemp_type" class="form-control"
                                placeholder="tasktemp_type">
                        </div>
                        <div id="templates_head_video" style="display: none;"></div>
                        <div class="form-group" id="templates_params" style="display: none;">

                        </div>
                        <div class="form-group" id="messages_media" style="display: none;">
                            <label>{{ __('Media') }} <small
                                    class="text-danger">{{ __('(Max Size 5 MB, Max Width 1024, MAX Height 768)') }}</small></label>
                            <input type="file" class="form-control" name="mediatemplate_file" id="mediatemplate_file"
                                disabled>
                        </div>
                        <div class="form-group" id="checktemplate_media" style="display: none;">
                            <input type="textarea" id="hastemplate_media" name="hastemplate_media" class="form-control"
                                placeholder="hastemplate_media">
                        </div>
                        <img class="card-img-top rounded mx-auto d-block" id="head_imagetemp" name="head_imagetemp"
                            alt="Image" style="width:60%;height:200px;display:none!important;">
                        <p class="card-text mt-2" id="campaign_texttemp" name="campaign_texttemp"
                            style="margin-bottom: 5px;"></p>
                        <div id="templates_buttons" class="text-center"></div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-outline-primary float-right submit-button" type="submit"><i
                                class="fi fi-rs-paper-plane"></i>&nbsp;&nbsp;{{ __('Sent') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {{-- select template with media and parameter ends --}}

    {{-- create tag modal starts --}}
    <div class="modal fade" id="create-new-tag" tabindex="-1" aria-labelledby="editModal" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">{{ __('Create Tag') }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>{{ __('Tag Name') }} <span style="color:red">*</span></label>
                        <input type="text" name="tagname" class="form-control" id="tagname" maxlength="50">
                    </div>
                    <div class="form-group">
                        <label>{{ __('Keyword to auto assign tag') }}</label>
                        <input type="text" name="keyword" class="form-control" id="keyword">
                    </div>
                    <div class="form-group">
                        <label>{{ __('Choose Color') }}</label>
                        <input type="color" name="color" width="10px" class="form-control" id="color">
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="submitBtn" class="btn btn-outline-primary float-right"
                        type="submit">{{ __('Create Tag') }}</button>
                </div>
            </div>
        </div>
    </div>
    {{-- create tag modal ends --}}

    {{-- search filter modal code starts --}}
    <div class="modal fade bd-example-modal-sm" id="search-status-tag" tabindex="-1" aria-labelledby="editModal"
        data-bs-keyboard="false" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm" style="margin-top: 162px; width: 278px; margin-left: 297px;">
            <div class="modal-content">
                <form class="ajaxform_instant_reload">
                    @csrf
                    <div class="modal-body" style="padding-bottom: 0px;">
                        <div class="form-group">
                            <label style="color:#5E72E4">{{ __('Select Day') }}</label>
                            <select class="form-control" id="chat_days" name="chat_days" data-toggle="select">
                                <option value="1">Today</option>
                                <option value="2">Yesterday</option>
                                <option value="7">7 Days</option>
                                <option value="15">15 Days</option>
                                <option value="30">30 Days</option>
                                <option value="90">Last 3 Months</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label style="color:#5E72E4">{{ __('Status') }}</label>
                            <select class="form-control" id="chat_status" name="chat_status" data-toggle="select">
                                <option value="all">All</option>
                                <option value="unread">Unread</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label style="color:#5E72E4">{{ __('Select Tag') }}</label>
                            <select class="form-control" id="chat_tagi" name="chat_tagi" data-toggle="select">
                                <option value="all">All</option>
                                @foreach ($tag_name as $tag)
                                    <option value="{{ $tag->id }}">{{ $tag->tag_name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </form>
                <button id="subButton">Notification subscribe</button>
            </div>
        </div>
    </div>

    <div class="modal fade" id="confirmDeleteModal" tabindex="-1" role="dialog" aria-labelledby="confirmDeleteLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content border-danger">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title text-white" id="confirmDeleteLabel">Confirm Delete</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete this message?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" id="confirmDeleteBtn" class="btn btn-danger">Delete</button>
                </div>
            </div>
        </div>
    </div>

    {{-- search filter modal code ends --}}

    <input type="hidden" id="uuid" value="{{ $device->uuid }}">
    <input type="hidden" id="base_url" value="{{ url('/') }}">
@endsection


@if (getUserPlanData('access_chat_list') == true)
    @push('js')
        {{-- this page script file location is public/assets/js/pages/chat/list.js --}}
        <script type="module" src="{{ asset('assets/js/firebase.js') }}"></script>
        <script src="{{ asset('assets/vendor/select2/dist/js/select2.min.js') }}"></script>
        <script type="text/javascript" src="{{ asset('assets/js/pages/chat/list.js') }}"></script>
        <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

        {{-- onclick plus button choose media file when select type plain-text file functionality --}}
        <script type="text/javascript">
            function open_file() {
                document.getElementById('mediafile').click();
            }
        </script>

        <script>
            $(".assign_agents").select2({
                placeholder: "{{ __('Select Agent') }}",
                allowClear: true
            });
        </script>

        <script>
            // save contact to phone
            function saveContact() {

                var contactName = document.getElementById('tagrecivername').value;
                var contactNumber = document.getElementById('tagrecivernumber').value;

                var forContNumber = '+' + contactNumber.trim();

                if (contactNumber) {
                    var contact = {
                        name: contactName ?? forContNumber,
                        phone: forContNumber
                    };
                    // create a vcard file
                    var vcard = "BEGIN:VCARD\nVERSION:4.0\nFN:" + contact.name + "\nTEL;TYPE=work,voice:" + contact.phone +
                        "\nEND:VCARD";
                    var blob = new Blob([vcard], {
                        type: "text/vcard"
                    });
                    var url = URL.createObjectURL(blob);

                    const newLink = document.createElement('a');
                    newLink.download = contact.name + ".vcf";
                    newLink.textContent = contact.name;
                    newLink.href = url;

                    newLink.click();
                } else {
                    alert("Select a contact to save.");
                    // console.log("No select contact to save.");
                }
            }

            function showMessage() {
                document.getElementById('rightModal').classList.add('show');
                document.getElementById('modalOverlay').classList.add('show');
            }

            function closeModal() {
                document.getElementById('rightModal').classList.remove('show');
                document.getElementById('modalOverlay').classList.remove('show');
            }

            // click to dial number
            function clickToDialNumber() {
                // Get the value of the hidden input
                var receiverNumber = document.getElementById('tagrecivernumber').value;

                // Check if the number exists
                if (receiverNumber) {
                    var formattedNumber = '+' + receiverNumber.trim();
                    // Example: Use the number by logging it to the console
                    //console.log("Receiver Number: " + formattedNumber);

                    // You can use this number for any other action
                    // For example, opening the dial pad:
                    window.location.href = "tel:" + formattedNumber;
                } else {
                    alert("Select a contact to dial.");
                    // console.log("No mobile number available.");
                }
            }

            // click to block number
            function blockNumber() {
                // Get the value of the hidden input
                var number = document.getElementById('tagrecivernumber').value;
                var uuid = document.getElementById('uuid').value;

                // Check if the number exists
                if (number) {

                    var data = {
                        number: number,
                        uuid: uuid
                    };

                    $.ajax({
                        url: "{{ route('user.blockUnblockUser') }}",
                        type: "POST",
                        data: data,
                        dataType: "JSON",
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(data) {
                            // console.log('blockNumber data', data);
                            if (data.success) {
                                getchathistory(number);
                                getChatList();
                            } else {
                                console.error("data not save");
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            // alertify.error('Error adding / update data');
                        }
                    });
                } else {
                    //alert("Select a contact to dial.");
                    //console.log("No mobile number available.");
                }
            }


            // click to unblock number
            function unblockNumber() {
                // Get the value of the hidden input
                var number = document.getElementById('tagrecivernumber').value;
                var uuid = document.getElementById('uuid').value;

                // Check if the number exists
                if (number) {

                    var data = {
                        number: number,
                        uuid: uuid,
                        status: 'unblock'
                    };

                    $.ajax({
                        url: "{{ route('user.blockUnblockUser') }}",
                        type: "POST",
                        data: data,
                        dataType: "JSON",
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(data) {
                            // console.log('unblockNumber data', data);

                            if (data.success) {
                                getchathistory(number);
                                getChatList();
                            } else {
                                console.error("data not save");
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.error("Error:", textStatus, errorThrown);
                            // alertify.error('Error adding / update data');
                        }
                    });
                } else {
                    //alert("Select a contact to dial.");
                    //console.log("No mobile number available.");
                }
            }
        </script>

        <script>
            $(document).ready(function() {
                $('#template-form').submit(function(e) {
                    e.preventDefault();
                    $('#tempselect').modal('hide');
                });
            });
        </script>

        <script>
            const modal_data = document.getElementById("template-form");
            modal_data.addEventListener("submit", refreshtempmodal);

            function refreshtempmodal(event) {
                setTimeout(function() {
                    var temp_data = document.getElementById('reciver').value;
                    getchathistory(temp_data)
                }, 2000);
            }

            setInterval(function() {
                var temp_data = document.getElementById('reciver').value;
                if (temp_data.length > 0) {
                    getchathistory(temp_data);
                }
            }, 60000); // 10000 milliseconds = 10 seconds
            setInterval(function() {
                getnewChatList();
            }, 65000); // 10000 milliseconds = 10 seconds
        </script>

        {{-- when send new message then auto refresh withough full reload  --}}
        <script>
            const form = document.getElementById("message-form");
            const plainText = document.getElementById("plain-text");

            // Handle actual form submission
            form.addEventListener("submit", function(event) {
                event.preventDefault(); // Prevent default form reload (optional if using AJAX)
                // Your logic here (e.g. AJAX send, or default submit)
                //form.submit(); // optional if you're NOT using AJAX

                // After submit logic
                setTimeout(function() {
                    var tag_number = document.getElementById('tagrecivernumber').value;
                    getchathistory(tag_number);
                    $("#mediafile").val('');
                    $("#rep_msg_id").val('');
                    cancelReply();
                }, 2000);
            });

            // Detect Ctrl + Enter (Windows/Linux) or Cmd + Enter (Mac)
            plainText.addEventListener("keydown", function(event) {
                if ((event.ctrlKey || event.metaKey) && (event.key === "Enter" || event.keyCode === 13)) {
                    event.preventDefault();
                    form.dispatchEvent(new Event("submit", {
                        bubbles: true,
                        cancelable: true
                    }));
                }
            });
        </script>

        {{-- store the color tag into database using bootstrap modal --}}
        <script>
            $(document).ready(function() {
                $('#create-new-tag').on('show.bs.modal', function() {
                    $("#tagname").val('');
                    $("#color").val('');
                    $("#keyword").val('');
                });
                $('#submitBtn').click(function(e) {
                    e.preventDefault();

                    var tag_name = $("#tagname").val();
                    var color_code = $("#color").val();
                    var keyword = $("#keyword").val();

                    var data = {
                        tagname: tag_name,
                        color: color_code,
                        keyword: keyword,
                    };

                    $.ajax({
                        url: "{{ route('user.tag.store') }}",
                        type: "POST",
                        data: data,
                        dataType: "JSON",
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(data) {

                            if (data.success) {
                                // hide the modal
                                $('#create-new-tag').modal('hide');
                                refreshoptdata();
                            } else {
                                console.error("data not save");
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            // alertify.error('Error adding / update data');
                        }
                    });
                });
            });

            //this function for create new tag then withought page refresh add data itno option value.
            function refreshoptdata() {
                $.ajax({
                    url: "{{ route('user.tag_option_data') }}",
                    type: "GET",
                    dataType: "json",
                    success: function(updatedData) {
                        // clear option
                        $('#tag_select').empty();
                        $('#tag_select').append('<option value="">Select tag</option');

                        // fetch data
                        for (var i = 0; i < updatedData.length; i++) {
                            $('#tag_select').append('<option value="' + updatedData[i].id + '">' + updatedData[i]
                                .tag_name + '</option>');
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        // Handle errors if necessary
                    }
                });
            }
        </script>

        {{-- update tag using axios method --}}
        <script>
            function updateTagAndClose() {
                var tagId = document.getElementById('tag_select').value;
                var tag_number = document.getElementById('tagrecivernumber').value;
                const dropdownMenu = document.querySelector('.dropdown-menu-media');
                dropdownMenu.classList.remove('show');
                axios.post("{{ route('user.store_contact_tag') }}", {
                        tag_id: tagId,
                        tag_number: tag_number,
                    })
                    .then(function(response) {
                        //  console.log(response.data.message);
                        getchathistory(tag_number);
                        getChatList();
                    })
                    .catch(function(error) {
                        console.error(error);
                    });
            }

            function assignChatAndClose() {
                // console.log(window.skipAssign);

                if (window.skipAssign) {
                    window.skipAssign = false; // reset for next real user interaction
                    return;
                }

                // var agentIds = document.getElementById('agent_ids').value;
                var agentIds = $('#agent_ids').val();
                var mobile_number = document.getElementById('tagrecivernumber').value;

                if (!agentIds || agentIds.length === 0) {
                    return; // Exit early if no agent is selected
                }

                if (mobile_number) {
                    var data = {
                        agent_ids: agentIds,
                        mobile_number: mobile_number
                    };

                    $.ajax({
                        url: "{{ route('user.update_assign_chat') }}",
                        type: "POST",
                        data: data,
                        dataType: "JSON",
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(data) {
                            // console.log('unblockNumber data', data);

                            if (data.success) {
                                getchathistory(mobile_number);
                                getChatList();
                            } else {
                                console.error("data not save");
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.error("Error:", textStatus, errorThrown);
                            // alertify.error('Error adding / update data');
                        }
                    });
                } else {
                    alert("Select a contact to assign chat.");
                }
            }

            function saveNoteMsg() {
                var replyMsg = document.getElementById('noteInput').value;
                var mobile_number = document.getElementById('tagrecivernumber').value;
                var rep_device_id = document.getElementById('rep_device_id').value;

                const dropdownMenu = document.querySelector('.dropdown-menu-media');
                dropdownMenu.classList.remove('show');

                if (mobile_number) {
                    var data = {
                        reply_msg: replyMsg,
                        mobile_number: mobile_number,
                        device_id: rep_device_id,
                    };

                    $.ajax({
                        url: "{{ route('user.send_note_msg') }}",
                        type: "POST",
                        data: data,
                        dataType: "JSON",
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(data) {
                            // console.log('unblockNumber data', data);
                            $('#noteInput').val('');
                            if (data.success) {
                                getchathistory(mobile_number);
                                getChatList();
                            } else {
                                console.error("data not save");
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.error("Error:", textStatus, errorThrown);
                            // alertify.error('Error adding / update data');
                        }
                    });
                } else {
                    alert("Select a contact to send note.");
                }
            }

            function cancelReply() {
                // Hide the reply box
                document.getElementById('reply-preview').classList.add('d-none');

                // Clear the values
                document.getElementById('rep_msg_id').value = '';
                document.getElementById('reply-sender').innerText = '';

            }

            function deleteMessage(primaryId, type) {
                // if (mobile_number) {
                // $('#reciver').val()
                var mobile_number = document.getElementById('reciver').value;
                var data = {
                    primary_id: primaryId,
                    type: type
                };

                $.ajax({
                    url: "{{ route('user.delete_chat_message') }}",
                    type: "POST",
                    data: data,
                    dataType: "JSON",
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(data) {
                        // console.log('unblockNumber data', data);
                        // $('#noteInput').val('');
                        if (data.success) {
                            getchathistory(mobile_number);
                            getChatList();
                        } else {
                            console.error("data not save");
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error("Error:", textStatus, errorThrown);
                        // alertify.error('Error adding / update data');
                    }
                });
                // } else {
                //     alert("Select a contact to send note.");
                // }
            }

            function preventPropagation(event) {
                event.stopPropagation();
            }
        </script>

        {{-- filter icon search filter  --}}
        <script>
            $('#search-status-tag').on('hidden.bs.modal', function() {

                var day = document.getElementById('chat_days').value;
                var status = document.getElementById('chat_status').value;
                var tag = document.getElementById('chat_tagi').value;
                var consearch = document.getElementById('consearch').value;
                page = 1;
                $('.contact-list').empty();
                getChatList();

            });
        </script>
        <script>
            let typingTimer; // Timer identifier
            const debounceTimeout = 1000; // Timeout in milliseconds (0.5 seconds)

            const inputElement = document.getElementById('consearch');

            // Attach the input event listener to the input element
            inputElement.addEventListener('input', onInputChange);

            function onTypingStop(inputValue) {
                if (inputValue.length > 3) {
                    getChatList();
                }
            }
            // Function to handle input event
            function onInputChange(event) {
                // Clear the previous timer
                clearTimeout(typingTimer);

                // Get the current value of the input field
                const inputValue = event.target.value;
                if (inputValue.length == 0) {
                    getChatList();
                }

                // Set a new timer to call the onTypingStop function
                typingTimer = setTimeout(() => {
                    onTypingStop(inputValue);
                }, debounceTimeout);
            }
        </script>

        {{-- this script for template message sent --}}
        <script>
            //function for formatted text like whatsapp
            function formatWhatsAppTextToHTML(text) {

                function escapeHTML(text) {
                    const replacements = {
                        '&': '&amp;',
                        '<': '&lt;',
                        '>': '&gt;',
                        '"': '&quot;',
                        "'": '&#39;'
                    };
                    return text.replace(/[&<>"']/g, char => replacements[char]);
                }

                // Replace newline characters with <br> tags
                text = text.replace(/\n/g, '<br>');

                // Convert asterisks to <b> tags for bold text
                text = text.replace(/\*(.*?)\*/g, '<b>$1</b>');

                // Convert underscores to <i> tags for italic text
                text = text.replace(/_(.*?)_/g, '<i>$1</i>');

                // Convert backticks to <code> tags for monospace text
                text = text.replace(/```(.*?)```/g, '<code>$1</code>');

                // Convert tildes to <strike> tags for strike-through text
                text = text.replace(/~~(.*?)~~/g, '<strike>$1</strike>');

                return text;
            }

            $(document).ready(function() {
                $("#templateselection").change(function() {
                    $("#temp_head_image").hide();
                    $("#messages_media").hide();
                    $("#head_imagetemp").hide();
                    var id = $("#templateselection").val();

                    var uuid = $("#campaign_devicetemp").val();
                    let templates_data = @json($templates_data);
                    for (var i = 0; i < templates_data.length; i++) {
                        if (templates_data[i]['name'] == id) {
                            var head_type = templates_data[i]['components'][0]['format'];
                            var temp_language = templates_data[i]['language'];

                            $("#temp_language").val(temp_language);

                            // Dynamically generate JavaScript code based on head_type
                            if (head_type === "IMAGE") {
                                var head_image =
                                    'https://cdn4.iconfinder.com/data/icons/file-extensions-1/64/pngs-512.png';
                                if (templates_data[i]['components'][0]['example']) {
                                    head_image = templates_data[i]['components'][0]['example']['header_handle'][
                                        0
                                    ];
                                }
                                var text = templates_data[i]['components'][1]['text'];

                                // Generate code for IMAGE
                                $("#temp_head_image").show();
                                $("#mediatemplate_file").removeAttr("disabled");
                                $("#messages_media").show();
                                $("#temphead_text").hide();
                                $("#head_imagetemp").show();
                                $("#hastemplate_media").val("1");
                                $("#tasktemp_type").val("2");

                                var image = new Image();
                                image.crossOrigin = 'anonymous';
                                image.src = head_imagetemp;
                                image.onload = function() {
                                    //print the image
                                    $("#head_imagetemp").attr("src", this.src);
                                    var c = document.createElement("canvas");
                                    c.width = this.width;
                                    c.height = this.height;
                                    this.ctx = c.getContext("2d");
                                    this.ctx.drawImage(this, 0, 0);
                                    try {
                                        var imageDat = this.ctx.getImageData(0, 0, this.width, this.height);
                                    } catch (e) {
                                        alert("Cross origin restricted access");
                                    }
                                };
                            } else if (head_type === "DOCUMENT") {
                                // Generate code for DOCUMENT
                                var head_document =
                                    'https://cdn4.iconfinder.com/data/icons/file-extensions-1/64/pdfs-512.png';
                                if (templates_data[i]['components'][0]['example']) {
                                    head_document = templates_data[i]['components'][0]['example'][
                                        'header_handle'
                                    ][0];
                                }

                                var text = templates_data[i]['components'][1]['text'];
                                $("#templates_head_video").show();
                                $("#mediatemplate_file").removeAttr("disabled");
                                $("#messages_media").show();
                                $("#temphead_text").hide();
                                $("#head_imagetemp").show();
                                $("#hastemplate_media").val("1");
                                $("#tasktemp_type").val("3");
                                $("#head_video").attr("src", head_document);

                                var image = new Image();
                                image.crossOrigin = 'anonymous';
                                image.src =
                                    'https://cdn4.iconfinder.com/data/icons/file-extensions-1/64/pdfs-512.png';
                                image.onload = function() {
                                    //print the image
                                    $("#head_imagetemp").attr("src", this.src);
                                    var c = document.createElement("canvas");
                                    c.width = this.width;
                                    c.height = this.height;
                                    this.ctx = c.getContext("2d");
                                    this.ctx.drawImage(this, 0, 0);
                                    try {
                                        var imageDat = this.ctx.getImageData(0, 0, 50, 50);
                                    } catch (e) {
                                        alert("Cross origin restricted access");
                                    }
                                };

                            } else if (head_type === "VIDEO") {
                                // Generate code for VIDEO
                                var head_video = 'https://cdn-icons-png.flaticon.com/512/9695/9695761.png';
                                if (templates_data[i]['components'][0]['example']) {
                                    head_video = templates_data[i]['components'][0]['example']['header_handle'][
                                        0
                                    ];
                                }
                                var text = templates_data[i]['components'][1]['text'];
                                $("#templates_head_video").show();
                                $("#mediatemplate_file").removeAttr("disabled");
                                $("#messages_media").show();
                                $("#temphead_text").hide();
                                $("#head_imagetemp").show();
                                $("#hastemplate_media").val("1");
                                $("#tasktemp_type").val("4");
                                $("#head_video").attr("src", head_video);
                                var image = new Image();
                                image.crossOrigin = 'anonymous';
                                image.src = 'https://cdn-icons-png.flaticon.com/512/136/136545.png';
                                image.onload = function() {
                                    //print the image
                                    $("#head_imagetemp").attr("src", this.src);
                                    var c = document.createElement("canvas");
                                    c.width = this.width;
                                    c.height = this.height;
                                    this.ctx = c.getContext("2d");
                                    this.ctx.drawImage(this, 0, 0);
                                    try {
                                        var imageDat = this.ctx.getImageData(0, 0, this.width, this.height);
                                    } catch (e) {
                                        alert("Cross origin restricted access");
                                    }
                                };
                            } else if (head_type === "TEXT") {
                                // Generate code for TEXT
                                var temphead_text = templates_data[i]['components'][0]['text'];
                                var text = templates_data[i]['components'][1]['text'];
                                $("#templates_head_text").show();
                                $("#hastemplate_media").val("0");
                                $("#temphead_text").val(temphead_text);
                                $("#tasktemp_type").val("1");
                                $("#temphead_text").show();
                                $("#head_imagetemp").hide();
                            } else {
                                // Handle other cases
                                $("#hastemplate_media").val("0");
                                $("#tasktemp_type").val("1");
                                var text = templates_data[i]['components'][0]['text'];
                            }
                            var campaignText = document.querySelector("#campaign_texttemp");
                            campaignText.innerHTML = formatWhatsAppTextToHTML(text);

                            templates_data[i]['components'].forEach(function(item, index) {

                                if (item['type'] == "BUTTONS") {

                                    var variable_text = '<div class="form-group form-float">';
                                    item['buttons'].forEach(function(item, index) {
                                        $("#templates_buttons").show();
                                        //delte all child elements of templates_params
                                        $("#templates_buttons").empty();
                                        // console.log('button found');
                                        var button_text = item['text'];
                                        var button_url = item['url'];
                                        var button_phone_number = item['phone_number'];
                                        var button_type = item['type'];
                                        // console.log(button_type);
                                        if (button_type == 'URL') {
                                            variable_text += '<a href="' + button_url +
                                                '" class="btn btn-sm btn-neutral btn-block" target="_blank"  margin-bottom:5px;" ><i class="fi fi-rs-arrow-up-right-from-square"></i>&nbsp;&nbsp;' +
                                                button_text + '</a>';

                                        } else if (button_type == 'QUICK_REPLY') {
                                            variable_text +=
                                                '<a href="#" class="btn btn-sm btn-neutral btn-block" target="_blank" margin-bottom:5px;" ><i class="fi fi-rs-reply-all"></i>&nbsp;&nbsp;' +
                                                button_text + '</a>';
                                        } else if (button_type == 'PHONE_NUMBER') {
                                            variable_text += '<a href="' + button_phone_number +
                                                '" class="btn btn-sm btn-neutral btn-block" target="_blank"  margin-bottom:5px;" ><i class="fi fi-rs-circle-phone-flip"></i>&nbsp;&nbsp;' +
                                                button_text + '</a>';

                                        }
                                        $("#templates_buttons").append(variable_text);
                                    });
                                } else {
                                    $("#templates_buttons").hide();
                                }
                            });
                        }
                    }
                    // Get campaign_texttemp value and extract variables
                    let variableText = document.querySelector("#campaign_texttemp").innerHTML;
                    variableText = variableText.replace(/{/gi, "[");
                    variableText = variableText.replace(/}/gi, "]");
                    var variables = variableText.match(/\[\[[^\s\[\]]+\]\]/g);
                    if (variables != null) {
                        $("#templates_params").show();
                        $("#templates_params").empty(); // Clear existing content

                        // Generate input fields for variables
                        var variable_text = '<div class="form-group row">';
                        for (var i = 0; i < variables.length; i++) {
                            var variable = variables[i].replace("[[", "").replace("]]", "");
                            variable_text +=
                                '<div class="col-sm-12"><input type="text" class="form-control" id="variable_' +
                                variable + '" name="variable[]" placeholder="Enter Value : ' + variable +
                                '" required></div>';
                        }
                        variable_text += '</div>';

                        $("#templates_params").append(variable_text); // Add input fields
                    } else {
                        $("#templates_params").hide();
                    }

                });
            });
        </script>

        {{-- record voice --}}
        {{-- <script>
            let mediaRecorder;
            let audioChunks = [];

            document.getElementById('record-button').addEventListener('click', async () => {
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    alert("Your browser does not support audio recording.");
                    return;
                }

                try {
                    const permissionStatus = await navigator.permissions.query({
                        name: 'microphone'
                    });

                    if (permissionStatus.state === 'denied') {
                        alert(
                            "Microphone access has been denied. Please allow microphone access in your browser settings."
                        );
                        return;
                    }

                    if (mediaRecorder && mediaRecorder.state === 'recording') {
                        mediaRecorder.stop();
                        document.getElementById('record-button').textContent = 'Record Voice';
                        return;
                    }

                    const stream = await navigator.mediaDevices.getUserMedia({
                        audio: true
                    });
                    mediaRecorder = new MediaRecorder(stream);
                    mediaRecorder.start();
                    document.getElementById('record-button').textContent = 'Stop Recording';

                    mediaRecorder.ondataavailable = (event) => {
                        audioChunks.push(event.data);
                    };

                    mediaRecorder.onstop = () => {
                        const audioBlob = new Blob(audioChunks, {
                            type: "audio/mpeg"
                        });
                        const audioUrl = URL.createObjectURL(audioBlob);
                        document.getElementById('audio').src = audioUrl;
                        document.getElementById('audio').hidden = false;

                        // Create a File object from the Blob
                        const audioFile = new File([audioBlob], 'recorded_audio.mp3', {
                            type: 'audio/mpeg',
                            lastModified: new Date().getTime()
                        });

                        // Create a DataTransfer object to set the file input value
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(audioFile);

                        // Set the file input's files
                        const mediaFileInput = document.getElementById('mediafile');
                        mediaFileInput.files = dataTransfer.files;

                        // Trigger change event on the file input
                        const event = new Event('change', {
                            bubbles: true
                        });
                        mediaFileInput.dispatchEvent(event);

                        // Store base64 in hidden input if needed
                        const reader = new FileReader();
                        reader.onloadend = () => {
                            document.getElementById('voice-message').value = reader.result;
                        };
                        reader.readAsDataURL(audioBlob);

                        // Clean up
                        audioChunks = [];
                        stream.getTracks().forEach(track => track.stop());
                    };
                } catch (error) {
                    console.error("Error accessing microphone:", error);
                    alert("Error accessing microphone. Please check your permissions and try again.");
                }
            });
        </script> --}}
    @endpush
@endif
