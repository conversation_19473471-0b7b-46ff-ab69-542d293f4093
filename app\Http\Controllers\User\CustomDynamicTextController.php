<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Jobs\DynamicInsertTaskJob;
use App\Models\TutorialVideo;
use App\Traits\Whatsapp;
use App\Models\Device;
use App\Rules\Phone;
use App\Models\Smstransaction;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Illuminate\Support\Str;
// use ZipArchive;
use Session;

class CustomDynamicTextController extends Controller
{
    use Whatsapp;

    //return custom text message view page
    public function index()
    {
        $devices = Device::where('user_id', Auth::id())->where('status', 1)->latest()->get();

        $device_data = [];
        $templates = [];
        $i = 0;
        foreach ($devices  as $device) {

            $templates = getFacebookTemplates($device->waid, $device->token) ?? [];

            $filtered_templates = [];

            foreach ($templates as $template) {
                if ($template['status'] == "APPROVED") {
                    foreach ($template['components'] as $component) {
                        if ($component['type'] == "BODY") {
                            $countText = substr_count($component['text'], "{{");
                            if ($countText > 0) {
                                $filtered_templates[] = $template;
                                break;
                            }
                        }
                        if ($component['type'] == "BUTTONS") {
                            foreach ($component['buttons'] as $button) {
                                if (strcasecmp($button['type'], "url") === 0) {
                                    $countUrl = substr_count($button['url'], "{{");
                                    if ($countUrl > 0) {
                                        $filtered_templates[] = $template;
                                        break 2;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            $id = $device->id;
            $device_data[$id]['uuid'] = $device->uuid;
            $device_data[$id]['templates'] = $filtered_templates;
            $device_data[$id]['id'] = $device->id;
            $device_data[$id]['name'] = $device->name;
            $device_data[$id]['phone'] = $device->phone;
            $i++;
        }

        $phoneCodes = file_exists('uploads/phonecode.json') ? json_decode(file_get_contents('uploads/phonecode.json')) : [];

        $videoTutorial = TutorialVideo::where('key', 'send_dynamic_whatsapp')->first();
        return view('user.dynamicsend.create', compact('device_data', 'phoneCodes', 'devices', 'templates', 'videoTutorial'));
    }

    public function DynamicCustomText(Request $request)
    {
        if (api_plan() && api_plan()->title == 'Api') {
            return redirect()->back()->with('error', 'Send dynamic message features is not available with your subscription');
        }

        $device_id = $request->input('campaign_device');
        $templateId = $request->input('template_select');
        $campaign_name = $request->input('campaign_name');
        $has_media = $request->input('has_media');
        $scheduled_on = $request->input('schedule_on') ?? now();
        $task_type = $request->input('task_type');

        if ($request->hasFile('media_file')) {
            $file = $request->file('media_file');
            $filename = uniqid() . '.' . $file->getClientOriginalExtension();
            $file->storeAs('whatsappmedia', $filename);
            $media_url = url('whatsappmedia/' . $filename);
            // dd($media_url);
        } else {
            $media_url = null;
        }

        if (!$request->hasFile('contact_file')) {
            return redirect()->back()->with('error', 'Contact File Not uploaded');
        } else {
            $data_file = $request->file('contact_file');
            if (!$data_file) {
                return redirect()->back()->with('error', 'Contact File Not uploaded');
            }
            $validator = Validator::make($request->all(), [
                'contact_file' => 'required|file|mimes:jpg,png,pdf,mp4,jpeg,xlsx,xls,csv|max:10240',
            ]);
            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator)->withInput();
            }
            if ($templateId == NULL || $templateId == "") {
                return redirect()->back()->with('error', 'Template Not Selected');
            }

            try {
                $data_file = $request->file('contact_file');
                $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($data_file->getPathname());
                // $sheetData = $spreadsheet->getActiveSheet()->toArray(null, true, true, true);
                $worksheet = $spreadsheet->getActiveSheet();
                $chunkSize = 1000; // Adjust the chunk size as needed
                $highestRow = $worksheet->getHighestRow();
                $delayMinutes = 0;
                for ($startRow = 1; $startRow <= $highestRow; $startRow += $chunkSize) {
                    $endRow = min($startRow + $chunkSize - 1, $highestRow);

                    $sheetData = $worksheet->rangeToArray(
                        sprintf('A%d:%s%d', $startRow, $worksheet->getHighestColumn(), $endRow),
                        null,
                        true,
                        true,
                        true
                    );
                    try {
                        dispatch(new DynamicInsertTaskJob($sheetData, $device_id, Auth::id(), $templateId, $campaign_name, $has_media, $scheduled_on, $task_type, $media_url, request()->ip()))->onQueue('high')->delay(now()->addMinutes($delayMinutes));
                        $delayMinutes += 2;
                    } catch (\Exception $e) {
                        return redirect()->back()->with('error', 'Error dispatching the job: ' . $e->getMessage());
                    }
                    // Process $sheetData here
                }
               
                return redirect(url('user/dynamic-sent-text-message'))->with('success', 'Send Task Successfully');
            } catch (\Exception $e) {
                return redirect()->back()->with('error', 'Error: ' . $e->getMessage());
            }
        }
    }
}
