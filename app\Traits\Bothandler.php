<?php

namespace App\Traits;

use App\Jobs\SendTaskJob;
use App\Models\Task;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use App\Jobs\BulkInsertTaskJob;
use App\Models\User;
use App\Models\Device;
use App\Models\Template;
use App\Models\Smstransaction;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Unique;
use PhpParser\Node\Expr\Print_;
use App\Traits\Whatsapp;

trait Bothandler
{
    use Whatsapp;

    public function handle_message($device, $from, $reply_content)
    {
        $ip = request()->ip();
        $launch_time = now();

        $content = json_decode($reply_content->reply, true);

        if (json_last_error() != JSON_ERROR_NONE) {
            // JSON decoding error
            Log::debug('Error decoding JSON: ' . json_last_error_msg());
        }
        if ($content['type'] == 'interactive') {
            $text = $content['interactive']['body']['text'];
            $list = $content['interactive'];

            //for custom button
        } elseif ($content['type'] == 'button') {
            $text = $content['text'];
            $buttons = $content;
        } else {
            // dd($content['text']);
            $text = $content['text'];
        }

        $media_url = $reply_content->media_url ?? null;
        if (isset($content['interactive']) && is_array($content['interactive'])) {
            // Check if type key exists
            if (isset($content['interactive']['type']) && $content['interactive']['type'] == 'flow') {
                $reply_co['interactive'] = $content['interactive'];
                $reply_msg = json_encode($reply_co);
            } else {
                $reply_msg = $this->waba_json_generate($text, $media_url, $buttons ?? null, $list ?? null);
            }
        } else {
            // If no interactive content, generate regular message
            $reply_msg = $this->waba_json_generate($text, $media_url, $buttons ?? null, $list ?? null);
        }
        // dd($reply_content);

        $waid = (string) Str::uuid();

        $taskdata = [
            'device_id' => $device->id,
            'created_by' => $device->user_id,
            'scheduled_on' => $launch_time,
            'task_url' => null,
            'send_to_number' => $from,
            'templateId' => "auto_reply",
            'is_reply' => 1,
            'task_type' => "1",
            'parameters' => null,
            'text' => $reply_msg,
            'ip' => $ip,
            'whatsapp_id' => $waid,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ];
        //dd($taskdata);
        try {
            DB::table('task')->insert($taskdata);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error Save Data : ' . $e->getMessage());
        }

        try {
            // dispatch(new BulkInsertTaskJob($taskdata, array($from)));
            // dispatch(new SendTaskJob($waid));
            //use high priority queue
            SendTaskJob::dispatch($waid)->onQueue('high');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error dispatching the job: ' . $e->getMessage());
        }
    }

    public function options_to_list($options, $variable_name, $old_input = null)
    {
        $sections = [];
        if ($variable_name == 'accounttype_code') {
            foreach ($options as $option) {
                foreach ($option as $title => $rows) {
                    $section = [
                        'title' => $title ?? 'Select an option',
                        'rows' => []
                    ];
                    foreach ($rows as $index => $row) {
                        $section['rows'][] = [
                            'id' => 'accounttype:' . $title . ',code:' . $row,
                            'title' => $row,
                            // 'description' => $row
                        ];
                        // if ($title == 'TradingAccounts') {
                        //     $section['rows'][] = [
                        //         'id' => 'accounttype' . ':Trading,code:' . $row,
                        //         'title' => $row,
                        //         // 'description' => $row
                        //     ];
                        // } else {
                        //     $section['rows'][] = [
                        //         'id' => 'accounttype' . ':Demat,code:' . $row,
                        //         'title' => $row,
                        //         // 'description' => $row
                        //     ];
                        // }
                    }
                    $sections[] = $section;
                }
            }
        } else {
            if ($this->is_multi_array($options)) {
                //  echo "The options array is multi-dimensional.";
                foreach ($options as $option) {
                    foreach ($option as $title => $rows) {
                        $section = [
                            'title' => $title ?? 'Select an option',
                            'rows' => []
                        ];
                        foreach ($rows as $index => $row) {
                            $section['rows'][] = [
                                'id' => $old_input . ',' . $variable_name . ':' . $row,
                                'title' => $row,
                                // 'description' => $row
                            ];
                        }
                        $sections[] = $section;
                    }
                }
            } else {
                // echo "The options array is single-dimensional.";
                //"options": [      "LEDGER",      "OUTSTANDINGPOSITION",      "LEDGERBALANCE"    ],

                $section = [
                    'title' => 'Select an option',
                    'rows' => []
                ];
                foreach ($options as $index => $row) {
                    $section['rows'][] = [
                        'id' => $old_input . ',' . $variable_name . ':' . $row,
                        'title' => $row,
                        // 'description' => $row
                    ];
                }
                $sections[] = $section;
            }
        }
        return $sections;
    }


    public function is_multi_array($array)
    {
        return is_array($array) && count($array) !== count($array, COUNT_RECURSIVE);
    }
}
