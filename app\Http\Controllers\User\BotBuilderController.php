<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Device;
use App\Models\TutorialVideo;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class BotBuilderController extends Controller
{
    public $user_id;

    public function index()
    {
        $devices = Device::with('user')->where('user_id', Auth::id())->withCount('tasks')->latest()->paginate(20);
        $videoTutorial = TutorialVideo::where('key', 'bot_builder')->first() ?? null;

        return view('user.bot_builder.index', compact('devices', 'videoTutorial'));
    }

    public function botbuilderDevice(Request $request)
    {
        
    }
}
