<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Device;
use App\Models\Report;
use App\Models\Task;
use App\Models\TutorialVideo;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ReportDataExport;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;

class ReportController extends Controller
{
    public function index(Request $request)
    {
        $todayDate = date('Y-m-d');
        $initialDateRange = date('m/d/Y') . ' - ' . date('m/d/Y');

        $camp_data = Task::where('created_by', Auth::id())
            ->select('campaign_name')
            ->distinct()
            ->pluck('campaign_name');

        $device_data = Device::where('user_id', Auth::id())
            ->where('status', 1)
            ->latest()
            ->get();

        // Fetch all counts in **one query**
        $taskStats = Task::where('created_by', Auth::id())
            ->selectRaw("
                COUNT(CASE WHEN task_status = 0 THEN 1 END) as pending_count,
                COUNT(CASE WHEN task_status = 1 THEN 1 END) as sent_count,
                COUNT(CASE WHEN task_status = 2 THEN 1 END) as delivered_count,
                COUNT(CASE WHEN task_status = 3 THEN 1 END) as read_count,
                COUNT(CASE WHEN task_status = 4 THEN 1 END) as failed_count,
                COUNT(CASE WHEN task_status = 0 AND DATE(created_at) = ? THEN 1 END) as todayPendingCount,
                COUNT(CASE WHEN task_status = 2 AND DATE(created_at) = ? THEN 1 END) as todayDeliveredCount,
                COUNT(CASE WHEN task_status = 3 AND DATE(created_at) = ? THEN 1 END) as todayReadCount,
                COUNT(CASE WHEN task_status = 4 AND DATE(created_at) = ? THEN 1 END) as todayFailCount
            ", [$todayDate, $todayDate, $todayDate, $todayDate])
            ->first();

        // Assign values from the query
        $pending_count = $taskStats->pending_count;
        $sent_count = $taskStats->sent_count;
        $delivered_count = $taskStats->delivered_count;
        $read_count = $taskStats->read_count;
        $failed_count = $taskStats->failed_count;
        $todayPendingCount = $taskStats->todayPendingCount;
        $todayDelieCount = $taskStats->todayDeliveredCount;
        $todayReadCount = $taskStats->todayReadCount;
        $todayFailCount = $taskStats->todayFailCount;
        // dd($pending_count);
        // dd($todayDelieCount);
        $videoTutorial = TutorialVideo::where('key', 'report')->first();
        return view('user.report.index', compact(
            'camp_data',
            'device_data',
            'pending_count',
            'sent_count',
            'delivered_count',
            'read_count',
            'failed_count',
            'initialDateRange',
            'videoTutorial',
            'todayPendingCount',
            'todayDelieCount',
            'todayReadCount',
            'todayFailCount'
        ));
    }

    public function get_data_filter(Request $request)
    {
        $start = $request->input("start");
        $length = $request->input("length");
        $draw = $request->input("draw");
        $search_value = $request->input("search");
        $columnindex = $request->input("order");
        $columnname = $request->input("columns");
        $columnsortorder = $request->input("order");
        $start_date = $request->input('start_date');
        $end_date = $request->input('end_date');
        $device_id = $request->input('select_device');
        $campaign_id = $request->input('select_campaign');

        $model = new Report();
        $response =  $model->get_data($start_date, $end_date, $draw, $start, $length, $search_value['value'], $columnname[$columnindex[0]['column']]['data'], $columnsortorder[0]['dir'], $device_id, $campaign_id);
        return $response;
    }

    // public function export_data(Request $request)
    // {
    //     $start_date = $request->input('start_date');
    //     $end_date = $request->input('end_date');
    //     $device_id = $request->input('select_device');
    //     $campaign_name = $request->input('select_campaign');

    //     // Inject the ReportModel
    //     $reportModel = new Report;

    //     $response = $reportModel->export_data($start_date, $end_date, $device_id, $campaign_name);

    //     $filename = 'report_tasks_' . $start_date . '_' . $end_date . '.xlsx';

    //     // Generate the Excel file
    //     return Excel::download(new ReportDataExport($response), $filename);
    // }
    public function export_data(Request $request)
    {
        $start_date    = $request->input('start_date');
        $end_date      = $request->input('end_date');
        $device_id     = $request->input('select_device');
        $campaign_name = $request->input('select_campaign');

        $filename = 'report_tasks_' . $start_date . '_' . $end_date . '.xlsx';

        return Excel::download(new ReportDataExport($start_date, $end_date, $device_id, $campaign_name), $filename);
    }
}
