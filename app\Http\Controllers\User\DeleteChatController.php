<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Device;
use App\Models\Messages;
use App\Models\Task;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class DeleteChatController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $devices = Device::where('user_id', Auth::id())->where('status', 1)->latest()->paginate(20);

        if (!$devices) {
            return response()->json(["message" => __("Device not found..!!"),], 200);
        }
        return view('user.deletechat.index', compact('devices'));
    }

    public function deleteChatHistory(Request $request)
    {
        try {
            $validated = $request->validate([
                'device_number' => 'required',
                'deletechat_daywise' => 'required',
                'chat_received_sent' => 'required',
            ]);

            $device_id = $request->input('device_number');
            $days = $request->input('deletechat_daywise');
            $type = $request->input('chat_received_sent');

            // Log::info("Device ID: $device_id, Days: $days, Type: $type");
            $device = Device::where("user_id", Auth::id())
                ->where("status", 1)
                ->where("id", $device_id)
                ->first();

            if (!$device) {
                return response()->json(["message" => __("Device not found..!!")], 200);
            }

            switch ($days) {
                case '1':
                    // Today
                    $startDate = now()->startOfDay();
                    $endDate = now()->endOfDay();
                    break;
                case '2':
                    // Yesterday
                    $startDate = now()->subDay()->startOfDay();
                    $endDate = now()->subDay()->endOfDay();
                    break;
                case '7':
                    // Last 7 Days
                    $startDate = now()->subDays(7);
                    $endDate = now()->endOfDay();
                    break;
                case '15':
                    // Last 15 Days
                    $startDate = now()->subDays(15);
                    $endDate = now()->endOfDay();
                    break;
                case '30':
                    // Last 1 Month
                    $startDate = now()->subMonth();
                    $endDate = now()->endOfDay();
                    break;
                case '180':
                    // Last 6 Months
                    $startDate = now()->subMonths(6);
                    $endDate = now()->endOfDay();
                    break;
                case 'older_than_180':
                    // Older Than 6 Months
                    $startDate = now()->subMonths(180);
                    $endDate = now()->endOfDay();
                    break;
                case 'all':
                    // All
                    $startDate = now()->subYears(100);
                    $endDate = now()->endOfDay();
                    break;
                default:
                    // Default
                    $startDate = null;
                    $endDate = null;
                    break;
            }

            // delete on select type
            if ($type == 'send' || $type == 'all') {
                // Delete from Task table
                Task::where('device_id', $device_id)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->delete();
            }

            if ($type == 'received' || $type == 'all') {
                // Delete from Messages table
                $messages = Messages::where('device_phone', $device->phone)
                    ->whereBetween('timestamp', [$startDate, $endDate])
                    ->get();

                foreach ($messages as $message) {
                    // Delete media file from messages table
                    $mediaPath = public_path('uploads/incomming_media/' . $message->media);
                    if (File::exists($mediaPath)) {
                        File::delete($mediaPath);
                    }
                }

                // Delete data from Messages table
                Messages::where('device_phone', $device->phone)
                    ->whereBetween('timestamp', [$startDate, $endDate])
                    ->delete();
            }

            $message = __('Chat deleted successfully..!!');
            if ($request->expectsJson()) {
                return response()->json(['message' => $message], 200);
            } else {
                return redirect()->route('user.deletechat.index')->with('success', $message);
            }
        } catch (\Exception $e) {
            Log::error("Error in deleteChatHistory: " . $e->getMessage());
            if ($request->expectsJson()) {
                return response()->json(["error" => $e->getMessage()], 500);
            } else {
                return redirect()->route('user.deletechat.index')->with('error', $e->getMessage());
            }
        }
    }

    public function deletePendingMsg(Request $request)
    {
        try {
            $validated = $request->validate([
                'device_id' => 'required'
            ]);

            $device_id = $request->device_id;

            Task::where('device_id', $device_id)
                ->where('task_status', 0) // pending status
                ->delete();

            $message = __('Pending message deleted successfully..!!');
            if ($request->expectsJson()) {
                return response()->json(['message' => $message], 200);
            } else {
                return redirect()->route('user.deletechat.index')->with('success', $message);
            }
        } catch (\Exception $e) {
            Log::error("Error in deleteChatHistory: " . $e->getMessage());
            if ($request->expectsJson()) {
                return response()->json(["error" => $e->getMessage()], 500);
            } else {
                return redirect()->route('user.deletechat.index')->with('error', $e->getMessage());
            }
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */


    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
