<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class DynamicInsertTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $data;
    protected $device_id;
    protected $created_by;
    protected $templateId;
    protected $campaign_name;
    protected $has_media;
    protected $scheduled_on;
    protected $task_type;
    protected  $media_url;
    protected $ip;
    /**
     * Create a new job instance.
     */
    public function __construct($data, $device_id, $created_by, $templateId, $campaign_name, $has_media, $scheduled_on, $task_type, $media_url, $ip)
    {
        $this->data = $data;
        $this->device_id = $device_id;
        $this->created_by = $created_by;
        $this->templateId = $templateId;
        $this->campaign_name = $campaign_name;
        $this->has_media = $has_media;
        $this->scheduled_on = $scheduled_on;
        $this->task_type = $task_type;
        $this->media_url = $media_url;
        $this->ip = $ip;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Define the batch size (1000 entries per batch)
        $batchSize = 500;
        $numberBatches = array_chunk($this->data, $batchSize);
        $whatsapp_ids = [];
        foreach ($numberBatches as $numberBatch) {
            $taskdata = [];

            foreach ($numberBatch as $row) {

                $variables = [];
                if ($row['A'] == "Mobile No" || $row['A'] == "" || $row['A'] == null) {
                    continue;
                }
                if (isset($row['B'])) {
                    $file_media_url = $row['B'];
                }
                $keys = range('C', 'L');
                $variables = [];

                foreach ($keys as $key) {
                    if (isset($row[$key])) {
                        $variables[] = $row[$key];
                    }
                }
                $variables = implode("||", $variables);
                $b1_type =  $row['M'];
                $b1_param = $row['N'];
                $b2_type =  $row['O'];
                $b2_param = $row['P'];
                $b3_type =  $row['Q'];
                $b3_param = $row['R'];

                $mask = isset($row['S']) ? $row['S'] : null;

                $buttons = [];
                if ($b1_type != "" && $b1_type != null && isset($b1_type)) {

                    $b1 = [
                        "type" => "button",
                        "sub_type" => $b1_type,
                        "index" => 0,
                        "parameters" => array(
                            array(
                                "type" => "payload",
                                "payload" => $b1_param
                            )
                        )
                    ];
                    array_push($buttons, $b1);
                }
                if ($b2_type != "" && $b2_type != null && isset($b2_type)) {

                    $b2 = array(
                        "type" => "button",
                        "sub_type" => $b2_type,
                        "index" => 1,
                        "parameters" => array(
                            array(
                                "type" => "payload",
                                "payload" => $b2_param
                            )
                        )
                    );
                    array_push($buttons, $b2);
                }
                if ($b3_type != "" && $b3_type != null && isset($b3_type)) {

                    $b3 = array(
                        "type" => "button",
                        "sub_type" => $b3_type,
                        "index" => 2,
                        "parameters" => array(
                            array(
                                "type" => "payload",
                                "payload" => $b3_param
                            )
                        )
                    );
                    array_push($buttons, $b3);
                }
                if (count($buttons) == 0) {
                    $buttons = NULL;
                } else {
                    $buttons = json_encode($buttons);
                }

                $uuid = (string) Str::uuid();
                $whatsapp_ids[] = $uuid;
                $masks[$uuid] = $mask;
                $taskdata[] = [
                    'device_id' => $this->device_id,
                    'created_by' => $this->created_by,
                    'scheduled_on' => $this->scheduled_on,
                    'send_to_number' => $row['A'],
                    'campaign_name' =>  $this->campaign_name ?? null,
                    'templateId' => $this->templateId,
                    'text' => $item['text'] ?? null,
                    'parameters' => $variables,
                    'task_url' =>  $this->media_url ?? $file_media_url ?? null,
                    'task_type' =>  $this->task_type,
                    'whatsapp_id' => $uuid,
                    'ip' => $this->ip,
                    'created_at' => now(),
                    'updated_at' => now()
                ];
            }

            try {
                DB::table('task')->insert($taskdata);
            } catch (\Exception $e) {
                // Handle any errors if needed
            }
        }

        foreach ($whatsapp_ids as $waid) {
            try {
                $mask = $masks[$waid] ?? null;

                dispatch(new SendTaskJob($waid, $mask))->delay(now()->setTimeFromTimeString($this->scheduled_on));
            } catch (\Exception $e) {
                // redirect()->back()->with('error', 'Error dispatching the job: ' . $e->getMessage());
            }
        }
    }
}
