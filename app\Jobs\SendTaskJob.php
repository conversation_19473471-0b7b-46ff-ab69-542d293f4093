<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Bus\Batchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use App\Traits\Whatsapp;
use Auth;

class SendTaskJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Whatsapp;

    protected $waid;
    protected $masking = null;

    /**
     * Create a new job instance.
     */
    public function __construct($waid, $masking = null)
    {
        $this->waid = $waid;
        $this->masking = $masking;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Use transaction for consistency
        DB::transaction(function () {
            $task = $this->getTaskDetails();

            if (!$task) {
                return;
            }

            $this->sendwhatsapp($task, $this->masking);
        });
    }

    private function getTaskDetails(): ?array
    {
        // Determine the database driver
        $driver = DB::connection()->getDriverName();
        $query = DB::table('task')
            ->select([
                'task.task_id',
                'task.is_reply',
                'task.created_by',
                'task.text',
                'task.scheduled_on',
                'task.send_to_number',
                'task.templateId',
                'task.task_url',
                'task.campaign_name',
                'task.parameters',
                'task.whatsapp_id',
                DB::raw("CASE task.task_type
                    WHEN 1 THEN 'text'
                    WHEN 2 THEN 'image'
                    WHEN 3 THEN 'document'
                    WHEN 4 THEN 'video'
                    ELSE 'UNKNOWN'
                END as task_type"),
                'devices.id',
                'devices.phone',
                'devices.status',
                'devices.waid',
                'devices.phoneid',
                'devices.token'
            ])
            ->join('devices', 'devices.id', '=', 'task.device_id')
            ->where('task.whatsapp_id', '=', $this->waid)
            ->where('devices.status', '=', '1');

        // Add the is_blacklisted field based on database driver
        if ($driver === 'pgsql') {
            // PostgreSQL version with FOR UPDATE SKIP LOCKED
            $query->addSelect(DB::raw('EXISTS (
                SELECT 1
                FROM blacklists b
                WHERE b.device_id = task.device_id
                AND b.sender_no = task.send_to_number
                FOR UPDATE SKIP LOCKED
            ) as is_blacklisted'));
        } else {
            // MySQL version without FOR UPDATE SKIP LOCKED
            $query->addSelect(DB::raw('EXISTS (
                SELECT 1
                FROM blacklists b
                WHERE b.device_id = task.device_id
                AND b.sender_no = task.send_to_number
            ) as is_blacklisted'));
        }

        return $query->get()->toArray();
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return ['whatsapp', 'device:' . $this->waid];
    }
}
