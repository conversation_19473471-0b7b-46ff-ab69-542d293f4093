<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use App\Traits\Whatsapp;
use App\Jobs\SendTaskJob;
use App\Models\User;
use App\Models\App;
use App\Models\Niwas;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NiwasController extends Controller
{
    use Whatsapp;

    public function niwas(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'appkey' => 'required',
            'authkey' => 'required',
            'lat_long' => 'required',
            'to' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors(),
            ], 400);
        }

        // dd($request->all());
        $user = User::where('status', 1)->where('will_expire', '>', now())->where('authkey', $request->authkey)->first();
        $app = App::where('key', $request->appkey)->whereHas('device')->with('device')->where('status', 1)->first();

        if ($user == null || $app == null) {
            return response()->json(['error' => 'Invalid Auth and AppKey'], 401);
        }

        if (getUserPlanData('messages_limit', $user->id) == false) {
            return response()->json([
                'message' => __('Maximum Monthly Messages Limit Exceeded')
            ], 401);
        }

        $latLong = explode(',', $request->lat_long);
        if (count($latLong) !== 2) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid lat_long format. Expected format: "latitude,longitude".'
            ], 400);
        }

        $lat1 = trim($latLong[0]);
        $long1 = trim($latLong[1]);

        if (!is_numeric($lat1) || !is_numeric($long1)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid latitude or longitude values.',
            ], 400);
        }

        $nearestLocation = DB::table('niwas')
            ->select(
                'id',
                'address',
                'latitude',
                'longitude',
                DB::raw("(6371 * acos(LEAST(1, GREATEST(-1, 
        CASE 
            WHEN latitude IS NULL OR longitude IS NULL THEN 0
            ELSE cos(radians($lat1::float)) * cos(radians(latitude::float)) * cos(radians(longitude::float) - radians($long1::float)) + sin(radians($lat1::float)) * sin(radians(latitude::float))
        END
    )))) AS distance")
            )
            ->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->whereRaw("latitude <> 'NULL'")
            ->whereRaw("longitude <> 'NULL'")
            ->orderBy('distance')
            ->first();

        if (!$nearestLocation) {
            return response()->json([
                'message' => 'No nearby locations found.',
                'status_code' => 404,
            ], 404);
        }

        // dd($nearestLocation);
        $address = $nearestLocation->address;
        $googleMapLink = "https://maps.google.com/?q={$nearestLocation->latitude},{$nearestLocation->longitude}";

        $text = "$address \n \n$googleMapLink";
        $replay =  $this->waba_json_generate($text);
        $uuid = (string) Str::uuid();
        $data = [
            'device_id' => $app->device_id,
            'created_by' => $app->user_id,
            'scheduled_on' => now(),
            'send_to_number' => $request->to,
            'templateId' => 'auto_reply',
            'is_reply' => 1,
            'text' => $replay,
            'task_type' => 1,
            'parameters' => null,
            // 'task_url' => $pdfUrl ?? null,
            'ip' => $request->getClientIp(),
            'whatsapp_id' => $uuid,
            // 'campaign_name' => 'Dp holding - ' . $accountid,
            'created_at' => now(),
            'updated_at' => now()
        ];
        try {
            DB::table('task')->insert($data);
            SendTaskJob::dispatch($uuid)->onQueue('high');
        } catch (\Exception $e) {
            return response()->json(['error' => 'Request Failed ' . $e], 401);
        }

        return response()->json([
            'message_status' => 'Success',
            'data' => [
                'device_phone' => $app->device->phone ?? null,
                'to' => $request->to,
                'data' => $text,
                // 'media' => $pdfUrl,
                // 'message' => $bodyMessage,
                'status_code' => 200,
            ]
        ], 200);
    }
}
