<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Task extends Model
{
    use HasFactory;

    protected $table = 'task';

    public $timestamps = true;
    protected $primaryKey = 'task_id';
    protected $fillable = [
        'created_by',
        'scheduled_on',
        'whatsapp_sent_time',
        'whatsapp_received_time',
        'whatsapp_read_time',
        'send_to_number',
        'templateId',
        'text',
        'parameters',
        'task_url',
        'task_type',
        'task_status',
        'is_reply',
        'task_description',
        'whatsapp_id',
        'ip'
    ];

    public function sentCustomText($data)
    {
        $this->fill($data);
        $this->save();
    }

    public function device()
    {
        return $this->belongsTo('App\Models\Device');
    }
}
