<?php

namespace App\Console\Commands;

use App\Models\ImTemplate;
use App\Jobs\BulkInsertTaskJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

class FetchIndiaMartLeads extends Command
{
    protected $signature = 'indiamart:fetch-leads';
    protected $description = 'Fetch new leads from IndiaMART API';

    public function handle()
    {
        // Get only active templates from active users
        $templates = ImTemplate::with('user')->get()
            ->filter(function ($template) {
                return optional($template->user)->will_expire > now();
            });

        foreach ($templates as $template) {
            $startTime = Carbon::parse($template->im_last_sync)->format('d-m-Y H:i:s');
            $endTime = now()->format('d-m-Y H:i:s');

            // Convert to Carbon instances for date manipulation
            $startDateTime = Carbon::createFromFormat('d-m-Y H:i:s', $startTime);
            $endDateTime = Carbon::createFromFormat('d-m-Y H:i:s', $endTime);


            // Check if the difference is greater than 6 days
            if ($startDateTime->diffInDays($endDateTime) > 6) {
                $startTime = $endDateTime->subDays(6)->format('d-m-Y H:i:s');
            }

            $response = Http::get('https://mapi.indiamart.com/wservce/crm/crmListing/v2/', [
                'glusr_crm_key' => $template->im_key,
                'start_time' => $startTime,
                'end_time' => $endTime
            ]);

            if ($response->successful() && $response['CODE'] == 200) {
                // Collect all mobile numbers from the response
                $mobileNumbers = collect($response['RESPONSE'])->map(function ($lead) {
                    return preg_replace('/[^0-9]/', '', $lead['SENDER_MOBILE']);
                })->filter()->values()->toArray();

                if (!empty($mobileNumbers)) {
                    $mediaUrl = $template->msg_media ?? null;
                    $taskData = [
                        'device_id' => $template->device_id,
                        'created_by' => $template->user_id,
                        'scheduled_on' => now(),
                        'task_url' => $mediaUrl,
                        'templateId' => $template->msg_template,
                        'task_type' => $template->tmp_type,
                        'parameters' => null,
                        'text' => null,
                        'ip' => request()->ip(),
                        'created_at' => now(),
                        'updated_at' => now()
                    ];

                    try {
                        // Dispatch single job with all mobile numbers
                        dispatch(new BulkInsertTaskJob($taskData, $mobileNumbers));
                        $this->info("Dispatched job with " . count($mobileNumbers) . " numbers for template ID: " . $template->id);
                    } catch (\Exception $e) {
                        $this->error("Error dispatching bulk job for template ID {$template->id}: {$e->getMessage()}");
                    }
                }

                // Update last sync time
                $template->update(['im_last_sync' => now()]);
            }
        }

        $this->info('IndiaMART leads fetch completed');
    }
}
